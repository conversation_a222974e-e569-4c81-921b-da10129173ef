.yoo-iconbox.yoo-style1 {
  border-radius: 10px;
  min-height: 140px;

  .yoo-iconbox-in {
    padding: 20px;
    position: relative;
    min-height: inherit;
    border-radius: inherit;
    background: linear-gradient(to left, rgba(255, 255, 255, 0) 0%, rgba(255, 244, 240, 0.3) 100%);
  }

  .yoo-iconbox-title {
    font-size: 12px;
    text-transform: uppercase;
    line-height: 1.6em;
    margin-top: -5px;
  }

  .yoo-iconbox-number {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2em;
  }

  .yoo-iconbox-footer {
    position: absolute;
    bottom: 14px;
    left: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;

    .yoo-iconbox-percentage {
      font-weight: 600;
      display: inline-flex;
      margin-right: 10px;

      i,
      .hydrated {
        font-size: 18px;
        position: relative;
        top: 2px;
        margin-right: 2px;
      }
    }
  }

  .yoo-iconbox-icon {
    position: absolute;
    right: 20px;
    top: 15px;
    display: flex;
    font-size: 40px;
    line-height: 1em;
  }

  &.yoo-color1 {
    background-color: $blue-color;

    .yoo-iconbox-title {
      color: rgba(255, 255, 255, 0.7);
    }

    .yoo-iconbox-number {
      color: #fff;
    }

    .yoo-iconbox-footer {
      color: rgba(255, 255, 255, 0.7);

      .yoo-iconbox-percentage {
        color: #fff;
      }
    }

    .yoo-iconbox-icon {
      color: #fff;
    }
  }

  &.yoo-color2 {
    background-color: #fff;
    .yoo-iconbox-in {
      background: inherit;
    }

    .yoo-iconbox-title {
      color: $base-color3;
    }

    .yoo-iconbox-number {
      color: $base-color1;
    }

    .yoo-iconbox-icon {
      color: $base-color3;
    }
  }

  &.yoo-type1 {
    .yoo-iconbox-footer {
      left: initial;
      right: 20px;
    }

    .yoo-iconbox-chart {
      max-width: 150px;
      margin-top: -30px;
      margin-bottom: -15px;
      margin-left: auto;
      margin-right: -10px;
    }

    .yoo-iconbox-icon {
      right: initial;
      left: 20px;
      bottom: 14px;
      top: initial;
    }
  }
}
