/*========== Start Pricing Table  ==========*/
.yoo-pricing-table.yoo-style1 {
  display: flex;
  padding-left: 1px;
  align-items: flex-end;
  margin: auto;

  .yoo-pricing-col {
    flex: 1;
    border: 1px solid $base-color4;
    margin-left: -1px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .yoo-pricing-row {
    text-align: center;
    min-height: 45px;
    border-bottom: 1px solid $base-color4;
    padding: 10px 15px;
    font-size: 14px;

    &:last-child {
      border-bottom: none;
    }
  }

  .yoo-pricing-title {
    flex: none;
    width: 280px;

    .yoo-pricing-row {
      text-align: left;
    }
  }

  .yoo-packeg-heading {
    background-color: $base-color5;
  }

  .yoo-price {
    font-size: 48px;
    margin-bottom: 16px;
    margin-top: 6px;
  }

  .yoo-price-currency {
    font-style: initial;
    font-size: 16px;
    position: relative;
    top: -8px;
    margin-right: 3px;
  }

  .yoo-btn {
    margin-bottom: 4px;
  }
}

.yoo-packeg.yoo-style1,
.yoo-packeg.yoo-style2 {

  .yoo-packeg-heading {
    background: $base-color5;
    border-bottom: 1px solid $base-color4;
    padding: 10px 30px;
    font-size: 14px;
  }

  .yoo-packeg-price {
    margin-bottom: 0;
    border-bottom: 1px solid $base-color4;
    font-size: 48px;
    font-weight: 500;
    padding: 23px 30px;
    display: flex;
    justify-content: center;
  }

  .yoo-packeg-currency {
    font-style: normal;
    font-size: 16px;
    position: relative;
    top: 11px;
    margin-right: 2px;
  }

  .yoo-packeg-per {
    padding-bottom: 7px;
    margin-left: 3px;
    display: flex;
    align-items: flex-end;
  }

  .yoo-packeg-btn {
    display: block;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.6em;
    border-top: 1px solid $base-color4;
    color: $base-color1;
    padding: 0.4em 1.5em;
    text-align: center;
  }

  .yoo-packeg-btn:hover {
    background-color: $base-color5;
  }

  &.yoo-featured-packeg {
    margin-top: -25px;

    .yoo-packeg-price {
      padding: 36px 30px 35px;
    }

    .yoo-packeg-btn {
      color: rgba(255, 255, 255, 0.9);
      background-color: $green-color;
      border-color: $green-color;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.yoo-packeg.yoo-style1,
.yoo-packeg.yoo-style2,
.yoo-packeg.yoo-style3 {
  border: 1px solid $base-color4;
  text-align: center;
}

.yoo-packeg-feature {
  font-size: 14px;
  padding: 25px 30px;

  li:not(:last-child) {
    margin-bottom: 12px;
  }

  .yoo-packeg-icon {
    margin-right: 8px;
    font-size: 12px;
  }
}

.yoo-packeg-per {
  font-size: 14px;
  color: $base-color3;
}

.yoo-packeg.yoo-style2 {
  text-align: left;

  .yoo-packeg-price {
    justify-content: flex-start;
  }
}

.yoo-packeg.yoo-style3 {
  padding: 15px;

  .yoo-packeg-img {
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .yoo-packeg-heading {
    font-size: 16px;
    line-height: 1.2em;
  }

  .yoo-packeg-price {
    font-size: 36px;
    margin-bottom: 0;
  }
}

.yoo-check {
  color: $green-color;
  font-size: 34px;
  display: flex;
  align-items: center;
  justify-content: center;

  i,
  i:before {
    line-height: inherit;
  }
}

.yoo-cross {
  color: $base-color3;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: center;

  i,
  i:before {
    line-height: inherit;
  }
}

.yoo-pricing-tab.yoo-style1 {
  .yoo-pricing-switch {
    margin-right: -30px;
    margin-left: -30px;
  }

  li {
    display: inline-flex;
    align-items: center;
    height: 32px;

    a {
      font-size: 15px;
      padding: 0 40px;
      height: 100%;
      display: flex;
      align-items: center;
    }

    &:first-child a {
      padding-left: 0;
    }

    &:last-child a {
      padding-right: 0;
    }
  }

  li.yoo-active {
    .yoo-pricing-switch {
      span {
        left: 3px;
      }
    }
  }
}

.yoo-pricing-switch {
  position: relative;
  height: 32px;
  width: 60px;
  background-color: $light-blue-color;
  border-radius: 16px;
  pointer-events: none;
  flex: none;

  span {
    display: block;
    height: 26px;
    width: 26px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: -0.382px 1.963px 3.92px 0.08px rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 31px;
    top: 3px;
    transition: all 0.3s ease;
  }
}

.yoo-pricing-tab.yoo-style2 {
  display: flex;
  justify-content: center;

  .yoo-tab-links {
    border: 2px solid $base-color3;
    display: inline-flex;
    border-radius: 5px;
  }

  a {
    min-width: 100px;
    display: inline-flex;
    justify-content: center;
    padding: 7px 10px;
    color: $base-color3;

    &:hover {
      background-color: $base-color5;
    }
  }

  .yoo-active {
    a {
      background-color: $base-color3;
      color: $main-color;
    }
  }

  li {
    &:first-child {
      a {
        border-radius: 3px 0 0 3px;
      }
    }

    &:last-child {
      a {
        border-radius: 0 3px 3px 0;
      }
    }
  }
}

.yoo-packeg.yoo-style3 .yoo-packeg-feature {
  padding-left: 0;
  padding-right: 0;
}

.nav-tabs.yoo-switch-tab {
  background: transparent;
  padding: 0;

  .nav-item .nav-link:after,
  .nav-item .nav-link:before {
    display: none;
  }

  .yoo-switch {
    position: absolute;
    z-index: 3;
    width: 60px;
    height: 32px;
    right: -30px;
    top: 2px;
    pointer-events: none;

    .yoo-switch-in {
      height: 26px;
      width: 26px;
      left: 31px;
      top: 3px;
    }
  }

  .nav-link.active+.yoo-switch {
    .yoo-switch-in {
      left: 3px;
    }
  }

  .nav-item {
    margin: 0 !important;

    &:first-child {
      .nav-link {
        padding-left: 10px;
        padding-right: 40px;
      }
    }

    &:last-child {
      .nav-link {
        padding-left: 40px;
        padding-right: 10px;
      }
    }
  }
  .nav-item .nav-link.active {
    box-shadow: none;
  }
}

.yoo-pricing-table {
  .yoo-check {
    display: inline-flex;
    font-size: 18px;
    position: relative;
    top: 4px;
  }

  .yoo-cross {
    display: inline-flex;
    font-size: 18px;
    position: relative;
    top: 4px;
  }
}

@media screen and (max-width: 991px) {
  .yoo-pricing-table.yoo-style1 {
    flex-direction: column;
  }

  .yoo-pricing-table.yoo-style1 .yoo-pricing-col {
    width: 100%;
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .yoo-pricing-table.yoo-style3 .row>div {
    padding-left: 0px;
    padding-right: 0px;
  }
}

/*========== End Pricing Table  ==========*/
