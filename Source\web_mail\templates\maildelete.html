<!DOCTYPE html>
<html class="no-js" lang="en">

<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
<script src="/assets/editor/editor.js"></script>
<link href="/assets/editor/editor.css" type="text/css" rel="stylesheet" />
{% include 'include/header.html' %}
    
<body>
  <!-- <div class="yoo-height-b60 yoo-height-lg-b60"></div>
  <div class="yoo-headersidebar-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div> -->
  <!-- .yoo-headersidebar-toggle -->

  {% include 'include/headersidebar.html' %}
  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title"></h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Xóa mail</h2>
              </div>
              <div class="yoo-card-heading-right btn-toolbar pull-right">
                <div class="row">
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-secondary btn-sm"
                    style="margin: 0px 2px 0px 0px; background-color: red;" onclick="$('#input').val('');">Loại bỏ</button>
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-sm" id="copy"
                  style="background-color: #04AA6D;" onclick="CopyTextareaToClipboard('input')">Sao chép</button>
                </div>
              </div>
            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <form>
                  <div class="form-group level-up">
                    <label for="exampleFormControlTextarea1"></label>
                    <textarea class="form-control" style="max-height: none;" id="input" rows="15"
                      placeholder="Nhập mỗi email 1 dòng. Định dạng: email|pass|emailkp hoặc chỉ cần email"></textarea>
                  </div>

                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-lg"
                    style="background-color: #04AA6D;" onclick="Xoa(event)">Xóa</button>
                  <br>
                  <br>
                </form>
                <div class="yoo-height-b5 yoo-height-lg-b5"></div>
              </div>
            </div>



          </div>
        </div>

      </div>
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->
      {% include 'include/footer.html' %}

    </div>
    <!-- .yoo-content -->
    <script>




      function Xoa() {
        var person = {
          listmail: $('#input').val(),
          groups: "",
        }
        $.ajax({
          url: "deletemail",
          type: "post",
          dataType: "json",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          data: JSON.stringify(person),
          contentType: 'application/json',
          success: function (result) {


            var msg = result.mess;

            if (msg == "success") {
              toastr.success("Xóa thành công");


            } else {
              toastr.error(msg);

            }

          }
        });
      }

    </script>

    <!-- Required Scripts -->
</body>

</html>