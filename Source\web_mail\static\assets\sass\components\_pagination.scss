.page-item .page-link {
  color: $blue-color;
  span {
    display: inline-flex;
  }
}

.page-link {
  color: $base-color2;
  border-color: $base-color4;
  background-color: $main-color;
  padding: 7px 10px;
  font-size: 14px;
  height: 36px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-item.disabled .page-link {
  background-color: $main-color;
  border-color: $base-color4;
}

.page-link:focus,
.page-link:hover {
  color: $blue-color;
  text-decoration: none;
  background-color: $base-color5;
  border-color: $base-color4;
}

.page-item.active .page-link {
  background-color: $blue-color;
  border-color: $blue-color;
}

.pagination {
  i,
  .hydrated {
    line-height: 1em;
    font-size: 18px;
  }
}

.page-link:focus {
  box-shadow: none;
}

.pagination-lg .page-link {
  padding: 10px 16px;
  height: 42px;
  font-size: 16px;
}

.pagination-sm .page-link {
  padding: 5px 11px;
  font-size: 13px;
  height: 30px;
}

.page-item:first-child .page-link {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.page-item:last-child .page-link {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
