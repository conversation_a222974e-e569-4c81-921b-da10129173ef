/*========== Start Footer  ==========*/
.yoo-footer {
  &.yoo-style1 {
    .yoo-footer-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid $base-color4;
      padding: 14px 0;
    }
  }
}

.yoo-footer-nav {
  li:not(:last-child) {
    margin-right: 30px;
  }

  a {
    font-weight: 500;
    color: $base-color2;

    &:hover {
      color: $blue-color;
    }
  }
}

.yoo-copyride {
  font-size: 13px;
  color: $base-color2;
  line-height: 1.6em;

  a {
    color: $blue-color;

    &:hover {
      color: rgba($blue-color, 0.8);
    }
  }
}

.yoo-sticky-footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  padding-left: 370px;
  background-color: #fff;
  z-index: 1;

  .container {
    position: relative;
  }

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: $base-color5;
  }
}
.yoo-footer.yoo-style1.yoo-white {
  .yoo-copyride,
  .yoo-footer-nav a {
    color: rgba(255, 255, 255, 0.7);
  }
  .yoo-footer-content {
    border-color: rgba(255, 255, 255, 0.1);
  }
}
@media screen and (max-width: 1199px) {
  .yoo-sticky-footer {
    padding-left: 0;
  }
}

@media screen and (max-width: 991px) {
  .yoo-footer.yoo-style1 .yoo-footer-content {
    flex-direction: column;
    text-align: center;
  }

  .yoo-sticky-footer {
    position: initial;
  }
}

/*========== End Footer  ==========*/
