/*========== Start User  ==========*/
.yoo-user-heading.yoo-style1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 30px 7px;
  margin-top: 0px;

  .yoo-user-title {
    font-size: 12px;
    text-transform: uppercase;
    color: $base-color3;
    font-weight: 500;
    margin: 0;
  }

  .yoo-user-options {
    font-size: 20px;
    display: flex;
    color: $base-color3;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      color: $base-color2;
    }

    .yoo-user-options-btn {
      display: inline-flex;
    }
  }

  &.yoo-type1 {
    padding-left: 0;
    padding-right: 0;
  }
}

// Style1
.yoo-user-list {
  .yoo-user.yoo-style1 {
    display: flex;
    align-items: center;
    padding: 10px 20px;
  }

  .yoo-user.yoo-style1:hover,
  .active .yoo-user.yoo-style1 {
    background-color: $base-color5;
  }

  .yoo-user-img {
    position: relative;
    border-radius: 50%;
    margin-right: 8px;
    flex: none;

    img {
      height: 34px;
      width: 34px;
      object-fit: cover;
      border-radius: 50%;
    }
  }

  .yoo-user-name {
    font-size: 14px;
    margin-bottom: 0px;
    font-weight: 400;
  }

  .yoo-user-time {
    display: block;
    font-size: 12px;
    color: $base-color3;
    line-height: 1.2;
  }

  .yoo-online-status {
    height: 8px;
    width: 8px;
    position: absolute;
    right: 0;
    bottom: 0;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
  }

  .yoo-group-member {
    font-size: 12px;
    line-height: 1.3em;
  }
}

// Style2
.yoo-user.yoo-style2 {
  display: flex;
  align-items: center;

  .yoo-user-img {
    border-radius: 50%;
    height: 34px;
    width: 34px;
    overflow: hidden;
    margin-right: 10px;
    flex: none;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .yoo-user-name {
    font-weight: 400;
    font-size: 16px;
    margin-bottom: 0;
    padding-top: 2px;
  }

  .yoo-online-status {
    height: 8px;
    width: 8px;
  }
}

// Style3
.yoo-user.yoo-style3 {
  display: flex;

  .yoo-user-img {
    border-radius: 50%;
    height: 36px;
    width: 36px;
    overflow: hidden;
    margin-right: 10px;
    flex: none;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .yoo-user-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 3px;
    padding-top: 8px;
  }

  .yoo-online-status {
    height: 8px;
    width: 8px;
  }

  .yoo-post-label.yoo-style1 {
    margin-bottom: -2px;
  }
}

// Style4
.yoo-user.yoo-style4 {
  display: flex;
  align-items: center;

  .yoo-user-img {
    margin-right: 20px;
    height: 128px;
    width: 128px;
    border-radius: 50%;
    overflow: hidden;
  }

  .yoo-user-name {
    font-size: 20px;
    margin-bottom: 0;
  }

  .yoo-lavel {
    font-size: 14px;
    line-height: 1.6em;
  }

  .yoo-user-btns {
    display: flex;
    flex-wrap: wrap;
    .btn {
      border-radius: 4px;
      &:focus {
        outline: none;
      }
    }
  }

  .btn {
    min-width: 140px;
    padding: 8px 15px;
    font-size: 14px;
    margin-right: 10px;
  }
}

.yoo-users.yoo-style1 {
  display: flex;
  padding-left: 5px;
  align-items: center;

  li {
    border-radius: 50%;
    overflow: hidden;
    background-color: #fff;
    height: 30px;
    width: 30px;
    margin-left: -5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 500;
    position: relative;

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #fff;
    }
  }
}

/*========== End User  ==========*/
