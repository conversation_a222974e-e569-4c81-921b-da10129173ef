/* File Link for Light Version */

/*========== Start Basic Typography  ==========*/
html {
  background-color: #fff; }

body {
  color: rgba(0, 0, 0, 0.7);
  font-family: "Inter", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6em;
  background-color: #f2f2f6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased; }

body,
div,
p,
span,
a {
  letter-spacing: -0.011em; }

h1,
h2,
h3,
h4,
h3,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h3,
.h5,
.h6 {
  clear: both;
  color: rgba(0, 0, 0, 0.95);
  padding: 0;
  margin: 0 0 10px 0;
  line-height: 1.2em;
  font-weight: 600;
  letter-spacing: -0.011em; }

h1,
.h1 {
  font-size: 40px; }

h2,
.h2 {
  font-size: 32px; }

h3,
.h3 {
  font-size: 28px; }

h4,
.h4 {
  font-size: 24px; }

h5,
.h5 {
  font-size: 20px;
  letter-spacing: 0; }

h6,
.h6 {
  font-size: 16px; }

p {
  margin-bottom: 12px; }

ul {
  margin: 0 0 15px 0;
  padding-left: 15px;
  list-style: square outside none; }

ol {
  padding-left: 15px;
  margin-bottom: 15px; }

dfn,
cite,
em,
i {
  font-style: italic; }

blockquote {
  margin: 0; }

address {
  margin: 0 0 15px; }

img {
  border: 0;
  max-width: 100%;
  height: auto; }

a {
  color: inherit;
  background-color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

a:hover,
a:active {
  outline: none;
  color: rgba(0, 0, 0, 0.95);
  text-decoration: none; }

hr {
  border-color: #e5e5e5;
  margin: 0; }

/*========== End Basic Typography  ==========*/
/*========== Start Default Spacing  ==========*/
@media screen and (min-width: 992px) {
  .yoo-height-b0 {
    height: 0px; }

  .yoo-height-b5 {
    height: 5px; }

  .yoo-height-b10 {
    height: 10px; }

  .yoo-height-b15 {
    height: 15px; }

  .yoo-height-b20 {
    height: 20px; }

  .yoo-height-b25 {
    height: 25px; }

  .yoo-height-b30 {
    height: 30px; }

  .yoo-height-b35 {
    height: 35px; }

  .yoo-height-b40 {
    height: 40px; }

  .yoo-height-b45 {
    height: 45px; }

  .yoo-height-b50 {
    height: 50px; }

  .yoo-height-b55 {
    height: 55px; }

  .yoo-height-b60 {
    height: 60px; }

  .yoo-height-b65 {
    height: 65px; }

  .yoo-height-b70 {
    height: 70px; }

  .yoo-height-b75 {
    height: 75px; }

  .yoo-height-b80 {
    height: 80px; }

  .yoo-height-b85 {
    height: 85px; }

  .yoo-height-b90 {
    height: 90px; }

  .yoo-height-b95 {
    height: 95px; }

  .yoo-height-b100 {
    height: 100px; }

  .yoo-height-b105 {
    height: 105px; }

  .yoo-height-b110 {
    height: 110px; }

  .yoo-height-b115 {
    height: 115px; }

  .yoo-height-b120 {
    height: 120px; }

  .yoo-height-b125 {
    height: 125px; }

  .yoo-height-b130 {
    height: 130px; }

  .yoo-height-b135 {
    height: 135px; }

  .yoo-height-b140 {
    height: 140px; }

  .yoo-height-b145 {
    height: 145px; }

  .yoo-height-b150 {
    height: 150px; }

  .yoo-height-b155 {
    height: 155px; }

  .yoo-height-b160 {
    height: 160px; }

  .yoo-height-b165 {
    height: 165px; }

  .yoo-height-b170 {
    height: 170px; }

  .yoo-height-b175 {
    height: 175px; }

  .yoo-height-b180 {
    height: 180px; }

  .yoo-height-b185 {
    height: 185px; }

  .yoo-height-b190 {
    height: 190px; }

  .yoo-height-b195 {
    height: 195px; }

  .yoo-height-b200 {
    height: 200px; }

  .yoo-height-b205 {
    height: 205px; }

  .yoo-height-b210 {
    height: 210px; } }
@media screen and (max-width: 991px) {
  .yoo-height-lg-b0 {
    height: 0px; }

  .yoo-height-lg-b5 {
    height: 5px; }

  .yoo-height-lg-b10 {
    height: 10px; }

  .yoo-height-lg-b15 {
    height: 15px; }

  .yoo-height-lg-b20 {
    height: 20px; }

  .yoo-height-lg-b25 {
    height: 25px; }

  .yoo-height-lg-b30 {
    height: 30px; }

  .yoo-height-lg-b35 {
    height: 35px; }

  .yoo-height-lg-b40 {
    height: 40px; }

  .yoo-height-lg-b45 {
    height: 45px; }

  .yoo-height-lg-b50 {
    height: 50px; }

  .yoo-height-lg-b55 {
    height: 55px; }

  .yoo-height-lg-b60 {
    height: 60px; }

  .yoo-height-lg-b65 {
    height: 65px; }

  .yoo-height-lg-b70 {
    height: 70px; }

  .yoo-height-lg-b75 {
    height: 75px; }

  .yoo-height-lg-b80 {
    height: 80px; }

  .yoo-height-lg-b85 {
    height: 85px; }

  .yoo-height-lg-b90 {
    height: 90px; }

  .yoo-height-lg-b95 {
    height: 95px; }

  .yoo-height-lg-b100 {
    height: 100px; }

  .yoo-height-lg-b105 {
    height: 105px; }

  .yoo-height-lg-b110 {
    height: 110px; }

  .yoo-height-lg-b115 {
    height: 115px; }

  .yoo-height-lg-b120 {
    height: 120px; }

  .yoo-height-lg-b125 {
    height: 125px; }

  .yoo-height-lg-b130 {
    height: 130px; }

  .yoo-height-lg-b135 {
    height: 135px; }

  .yoo-height-lg-b140 {
    height: 140px; }

  .yoo-height-lg-b145 {
    height: 145px; }

  .yoo-height-lg-b150 {
    height: 150px; }

  .yoo-height-lg-b155 {
    height: 155px; }

  .yoo-height-lg-b160 {
    height: 160px; }

  .yoo-height-lg-b165 {
    height: 165px; }

  .yoo-height-lg-b170 {
    height: 170px; }

  .yoo-height-lg-b175 {
    height: 175px; }

  .yoo-height-lg-b180 {
    height: 180px; }

  .yoo-height-lg-b185 {
    height: 185px; }

  .yoo-height-lg-b190 {
    height: 190px; }

  .yoo-height-lg-b195 {
    height: 195px; }

  .yoo-height-lg-b200 {
    height: 200px; }

  .yoo-height-lg-b205 {
    height: 205px; }

  .yoo-height-lg-b210 {
    height: 210px; } }
/*========== End Default Spacing  ==========*/
/*========== Start Margin Classes  ==========*/
.yoo-margin-0 {
  margin: 0; }

.yoo-margin-1 {
  margin: 1px; }

.yoo-margin-2 {
  margin: 2px; }

.yoo-margin-3 {
  margin: 3px; }

.yoo-margin-4 {
  margin: 4px; }

.yoo-margin-5 {
  margin: 5px; }

.yoo-margin-6 {
  margin: 6px; }

.yoo-margin-7 {
  margin: 7px; }

.yoo-margin-8 {
  margin: 8px; }

.yoo-margin-9 {
  margin: 9px; }

.yoo-margin-10 {
  margin: 10px; }

.yoo-margin-11 {
  margin: 11px; }

.yoo-margin-12 {
  margin: 12px; }

.yoo-margin-13 {
  margin: 13px; }

.yoo-margin-14 {
  margin: 14px; }

.yoo-margin-15 {
  margin: 15px; }

.yoo-margin-top-0 {
  margin-top: 0; }

.yoo-margin-top-1 {
  margin-top: 1px; }

.yoo-margin-top-2 {
  margin-top: 2px; }

.yoo-margin-top-3 {
  margin-top: 3px; }

.yoo-margin-top-4 {
  margin-top: 4px; }

.yoo-margin-top-5 {
  margin-top: 5px; }

.yoo-margin-top-6 {
  margin-top: 6px; }

.yoo-margin-top-7 {
  margin-top: 7px; }

.yoo-margin-top-8 {
  margin-top: 8px; }

.yoo-margin-top-9 {
  margin-top: 9px; }

.yoo-margin-top-10 {
  margin-top: 10px; }

.yoo-margin-top-11 {
  margin-top: 11px; }

.yoo-margin-top-12 {
  margin-top: 12px; }

.yoo-margin-top-13 {
  margin-top: 13px; }

.yoo-margin-top-14 {
  margin-top: 14px; }

.yoo-margin-top-15 {
  margin-top: 15px; }

.yoo-margin-bottom-0 {
  margin-bottom: 0; }

.yoo-margin-bottom-1 {
  margin-bottom: 1px; }

.yoo-margin-bottom-2 {
  margin-bottom: 2px; }

.yoo-margin-bottom-3 {
  margin-bottom: 3px; }

.yoo-margin-bottom-4 {
  margin-bottom: 4px; }

.yoo-margin-bottom-5 {
  margin-bottom: 5px; }

.yoo-margin-bottom-6 {
  margin-bottom: 6px; }

.yoo-margin-bottom-7 {
  margin-bottom: 7px; }

.yoo-margin-bottom-8 {
  margin-bottom: 8px; }

.yoo-margin-bottom-9 {
  margin-bottom: 9px; }

.yoo-margin-bottom-10 {
  margin-bottom: 10px; }

.yoo-margin-bottom-11 {
  margin-bottom: 11px; }

.yoo-margin-bottom-12 {
  margin-bottom: 12px; }

.yoo-margin-bottom-13 {
  margin-bottom: 13px; }

.yoo-margin-bottom-14 {
  margin-bottom: 14px; }

.yoo-margin-bottom-15 {
  margin-bottom: 15px; }

.yoo-margin-left-0 {
  margin-left: 0; }

.yoo-margin-left-1 {
  margin-left: 1px; }

.yoo-margin-left-2 {
  margin-left: 2px; }

.yoo-margin-left-3 {
  margin-left: 3px; }

.yoo-margin-left-4 {
  margin-left: 4px; }

.yoo-margin-left-5 {
  margin-left: 5px; }

.yoo-margin-left-6 {
  margin-left: 6px; }

.yoo-margin-left-7 {
  margin-left: 7px; }

.yoo-margin-left-8 {
  margin-left: 8px; }

.yoo-margin-left-9 {
  margin-left: 9px; }

.yoo-margin-left-10 {
  margin-left: 10px; }

.yoo-margin-left-11 {
  margin-left: 11px; }

.yoo-margin-left-12 {
  margin-left: 12px; }

.yoo-margin-left-13 {
  margin-left: 13px; }

.yoo-margin-left-14 {
  margin-left: 14px; }

.yoo-margin-left-15 {
  margin-left: 15px; }

.yoo-margin-right-0 {
  margin-right: 0; }

.yoo-margin-right-1 {
  margin-right: 1px; }

.yoo-margin-right-2 {
  margin-right: 2px; }

.yoo-margin-right-3 {
  margin-right: 3px; }

.yoo-margin-right-4 {
  margin-right: 4px; }

.yoo-margin-right-5 {
  margin-right: 5px; }

.yoo-margin-right-6 {
  margin-right: 6px; }

.yoo-margin-right-7 {
  margin-right: 7px; }

.yoo-margin-right-8 {
  margin-right: 8px; }

.yoo-margin-right-9 {
  margin-right: 9px; }

.yoo-margin-right-10 {
  margin-right: 10px; }

.yoo-margin-right-11 {
  margin-right: 11px; }

.yoo-margin-right-12 {
  margin-right: 12px; }

.yoo-margin-right-13 {
  margin-right: 13px; }

.yoo-margin-right-14 {
  margin-right: 14px; }

.yoo-margin-right-15 {
  margin-right: 15px; }

/*========== End Margin Classes  ==========*/
/*========== Start Padidng Classes  ==========*/
.yoo-padding-0 {
  padding: 0; }

.yoo-padding-1 {
  padding: 1px; }

.yoo-padding-2 {
  padding: 2px; }

.yoo-padding-3 {
  padding: 3px; }

.yoo-padding-4 {
  padding: 4px; }

.yoo-padding-5 {
  padding: 5px; }

.yoo-padding-6 {
  padding: 6px; }

.yoo-padding-7 {
  padding: 7px; }

.yoo-padding-8 {
  padding: 8px; }

.yoo-padding-9 {
  padding: 9px; }

.yoo-padding-10 {
  padding: 10px; }

.yoo-padding-11 {
  padding: 11px; }

.yoo-padding-12 {
  padding: 12px; }

.yoo-padding-13 {
  padding: 13px; }

.yoo-padding-14 {
  padding: 14px; }

.yoo-padding-15 {
  padding: 15px; }

.yoo-padding-top-0 {
  padding-top: 0; }

.yoo-padding-top-1 {
  padding-top: 1px; }

.yoo-padding-top-2 {
  padding-top: 2px; }

.yoo-padding-top-3 {
  padding-top: 3px; }

.yoo-padding-top-4 {
  padding-top: 4px; }

.yoo-padding-top-5 {
  padding-top: 5px; }

.yoo-padding-top-6 {
  padding-top: 6px; }

.yoo-padding-top-7 {
  padding-top: 7px; }

.yoo-padding-top-8 {
  padding-top: 8px; }

.yoo-padding-top-9 {
  padding-top: 9px; }

.yoo-padding-top-10 {
  padding-top: 10px; }

.yoo-padding-top-11 {
  padding-top: 11px; }

.yoo-padding-top-12 {
  padding-top: 12px; }

.yoo-padding-top-13 {
  padding-top: 13px; }

.yoo-padding-top-14 {
  padding-top: 14px; }

.yoo-padding-top-15 {
  padding-top: 15px; }

.yoo-padding-bottom-0 {
  padding-bottom: 0; }

.yoo-padding-bottom-1 {
  padding-bottom: 1px; }

.yoo-padding-bottom-2 {
  padding-bottom: 2px; }

.yoo-padding-bottom-3 {
  padding-bottom: 3px; }

.yoo-padding-bottom-4 {
  padding-bottom: 4px; }

.yoo-padding-bottom-5 {
  padding-bottom: 5px; }

.yoo-padding-bottom-6 {
  padding-bottom: 6px; }

.yoo-padding-bottom-7 {
  padding-bottom: 7px; }

.yoo-padding-bottom-8 {
  padding-bottom: 8px; }

.yoo-padding-bottom-9 {
  padding-bottom: 9px; }

.yoo-padding-bottom-10 {
  padding-bottom: 10px; }

.yoo-padding-bottom-11 {
  padding-bottom: 11px; }

.yoo-padding-bottom-12 {
  padding-bottom: 12px; }

.yoo-padding-bottom-13 {
  padding-bottom: 13px; }

.yoo-padding-bottom-14 {
  padding-bottom: 14px; }

.yoo-padding-bottom-15 {
  padding-bottom: 15px; }

.yoo-padding-left-0 {
  padding-left: 0; }

.yoo-padding-left-1 {
  padding-left: 1px; }

.yoo-padding-left-2 {
  padding-left: 2px; }

.yoo-padding-left-3 {
  padding-left: 3px; }

.yoo-padding-left-4 {
  padding-left: 4px; }

.yoo-padding-left-5 {
  padding-left: 5px; }

.yoo-padding-left-6 {
  padding-left: 6px; }

.yoo-padding-left-7 {
  padding-left: 7px; }

.yoo-padding-left-8 {
  padding-left: 8px; }

.yoo-padding-left-9 {
  padding-left: 9px; }

.yoo-padding-left-10 {
  padding-left: 10px; }

.yoo-padding-left-11 {
  padding-left: 11px; }

.yoo-padding-left-12 {
  padding-left: 12px; }

.yoo-padding-left-13 {
  padding-left: 13px; }

.yoo-padding-left-14 {
  padding-left: 14px; }

.yoo-padding-left-15 {
  padding-left: 15px; }

.yoo-padding-right-0 {
  padding-right: 0; }

.yoo-padding-right-1 {
  padding-right: 1px; }

.yoo-padding-right-2 {
  padding-right: 2px; }

.yoo-padding-right-3 {
  padding-right: 3px; }

.yoo-padding-right-4 {
  padding-right: 4px; }

.yoo-padding-right-5 {
  padding-right: 5px; }

.yoo-padding-right-6 {
  padding-right: 6px; }

.yoo-padding-right-7 {
  padding-right: 7px; }

.yoo-padding-right-8 {
  padding-right: 8px; }

.yoo-padding-right-9 {
  padding-right: 9px; }

.yoo-padding-right-10 {
  padding-right: 10px; }

.yoo-padding-right-11 {
  padding-right: 11px; }

.yoo-padding-right-12 {
  padding-right: 12px; }

.yoo-padding-right-13 {
  padding-right: 13px; }

.yoo-padding-right-14 {
  padding-right: 14px; }

.yoo-padding-right-15 {
  padding-right: 15px; }

/*========== End Padidng Classes  ==========*/
/*========== Start General Style  ==========*/
.container-fluid {
  padding-left: 30px;
  padding-right: 30px; }

/* Margin Classes */
.yoo-margin-0 {
  margin: 0; }

.yoo-margin-1 {
  margin: 1px; }

.yoo-margin-2 {
  margin: 2px; }

.yoo-margin-3 {
  margin: 3px; }

.yoo-margin-4 {
  margin: 4px; }

.yoo-margin-5 {
  margin: 5px; }

.yoo-margin-6 {
  margin: 6px; }

.yoo-margin-7 {
  margin: 7px; }

.yoo-margin-8 {
  margin: 8px; }

.yoo-margin-9 {
  margin: 9px; }

.yoo-margin-10 {
  margin: 10px; }

.yoo-margin-11 {
  margin: 11px; }

.yoo-margin-12 {
  margin: 12px; }

.yoo-margin-13 {
  margin: 13px; }

.yoo-margin-14 {
  margin: 14px; }

.yoo-margin-15 {
  margin: 15px; }

.yoo-margin-top-0 {
  margin-top: 0; }

.yoo-margin-top-1 {
  margin-top: 1px; }

.yoo-margin-top-2 {
  margin-top: 2px; }

.yoo-margin-top-3 {
  margin-top: 3px; }

.yoo-margin-top-4 {
  margin-top: 4px; }

.yoo-margin-top-5 {
  margin-top: 5px; }

.yoo-margin-top-6 {
  margin-top: 6px; }

.yoo-margin-top-7 {
  margin-top: 7px; }

.yoo-margin-top-8 {
  margin-top: 8px; }

.yoo-margin-top-9 {
  margin-top: 9px; }

.yoo-margin-top-10 {
  margin-top: 10px; }

.yoo-margin-top-11 {
  margin-top: 11px; }

.yoo-margin-top-12 {
  margin-top: 12px; }

.yoo-margin-top-13 {
  margin-top: 13px; }

.yoo-margin-top-14 {
  margin-top: 14px; }

.yoo-margin-top-15 {
  margin-top: 15px; }

.yoo-font-size-5 {
  font-size: 5px; }

.yoo-font-size-6 {
  font-size: 6px; }

.yoo-font-size-7 {
  font-size: 7px; }

.yoo-font-size-8 {
  font-size: 8px; }

.yoo-font-size-9 {
  font-size: 9px; }

.yoo-font-size-10 {
  font-size: 10px; }

.yoo-font-size-11 {
  font-size: 11px; }

.yoo-font-size-12 {
  font-size: 12px; }

.yoo-font-size-13 {
  font-size: 13px; }

.yoo-font-size-14 {
  font-size: 14px; }

.yoo-font-size-15 {
  font-size: 15px; }

.yoo-font-size-16 {
  font-size: 16px; }

.yoo-font-size-17 {
  font-size: 17px; }

.yoo-font-size-18 {
  font-size: 18px; }

.yoo-font-size-19 {
  font-size: 19px; }

.yoo-font-size-20 {
  font-size: 20px; }

.yoo-font-size-21 {
  font-size: 21px; }

.yoo-font-size-22 {
  font-size: 22px; }

.yoo-font-size-23 {
  font-size: 23px; }

.yoo-font-size-24 {
  font-size: 24px; }

.yoo-font-size-25 {
  font-size: 25px; }

.yoo-font-size-26 {
  font-size: 26px; }

.yoo-font-size-27 {
  font-size: 27px; }

.yoo-font-size-28 {
  font-size: 28px; }

.yoo-font-size-29 {
  font-size: 29px; }

.yoo-font-size-30 {
  font-size: 30px; }

.yoo-line-1-6 {
  line-height: 1.6em; }

.yoo-line-1-2 {
  line-height: 1.2em; }

.yoo-margin-bottom-0 {
  margin-bottom: 0; }

.yoo-margin-bottom-1 {
  margin-bottom: 1px; }

.yoo-margin-bottom-2 {
  margin-bottom: 2px; }

.yoo-margin-bottom-3 {
  margin-bottom: 3px; }

.yoo-margin-bottom-4 {
  margin-bottom: 4px; }

.yoo-margin-bottom-5 {
  margin-bottom: 5px; }

.yoo-margin-bottom-6 {
  margin-bottom: 6px; }

.yoo-margin-bottom-7 {
  margin-bottom: 7px; }

.yoo-margin-bottom-8 {
  margin-bottom: 8px; }

.yoo-margin-bottom-9 {
  margin-bottom: 9px; }

.yoo-margin-bottom-10 {
  margin-bottom: 10px; }

.yoo-margin-bottom-11 {
  margin-bottom: 11px; }

.yoo-margin-bottom-12 {
  margin-bottom: 12px; }

.yoo-margin-bottom-13 {
  margin-bottom: 13px; }

.yoo-margin-bottom-14 {
  margin-bottom: 14px; }

.yoo-margin-bottom-15 {
  margin-bottom: 15px; }

.yoo-margin-left-0 {
  margin-left: 0; }

.yoo-margin-left-1 {
  margin-left: 1px; }

.yoo-margin-left-2 {
  margin-left: 2px; }

.yoo-margin-left-3 {
  margin-left: 3px; }

.yoo-margin-left-4 {
  margin-left: 4px; }

.yoo-margin-left-5 {
  margin-left: 5px; }

.yoo-margin-left-6 {
  margin-left: 6px; }

.yoo-margin-left-7 {
  margin-left: 7px; }

.yoo-margin-left-8 {
  margin-left: 8px; }

.yoo-margin-left-9 {
  margin-left: 9px; }

.yoo-margin-left-10 {
  margin-left: 10px; }

.yoo-margin-left-11 {
  margin-left: 11px; }

.yoo-margin-left-12 {
  margin-left: 12px; }

.yoo-margin-left-13 {
  margin-left: 13px; }

.yoo-margin-left-14 {
  margin-left: 14px; }

.yoo-margin-left-15 {
  margin-left: 15px; }

.yoo-margin-right-0 {
  margin-right: 0; }

.yoo-margin-right-1 {
  margin-right: 1px; }

.yoo-margin-right-2 {
  margin-right: 2px; }

.yoo-margin-right-3 {
  margin-right: 3px; }

.yoo-margin-right-4 {
  margin-right: 4px; }

.yoo-margin-right-5 {
  margin-right: 5px; }

.yoo-margin-right-6 {
  margin-right: 6px; }

.yoo-margin-right-7 {
  margin-right: 7px; }

.yoo-margin-right-8 {
  margin-right: 8px; }

.yoo-margin-right-9 {
  margin-right: 9px; }

.yoo-margin-right-10 {
  margin-right: 10px; }

.yoo-margin-right-11 {
  margin-right: 11px; }

.yoo-margin-right-12 {
  margin-right: 12px; }

.yoo-margin-right-13 {
  margin-right: 13px; }

.yoo-margin-right-14 {
  margin-right: 14px; }

.yoo-margin-right-15 {
  margin-right: 15px; }

/* End Margin Classes */
/* Padidng Classes */
.yoo-padding-0 {
  padding: 0; }

.yoo-padding-1 {
  padding: 1px; }

.yoo-padding-2 {
  padding: 2px; }

.yoo-padding-3 {
  padding: 3px; }

.yoo-padding-4 {
  padding: 4px; }

.yoo-padding-5 {
  padding: 5px; }

.yoo-padding-6 {
  padding: 6px; }

.yoo-padding-7 {
  padding: 7px; }

.yoo-padding-8 {
  padding: 8px; }

.yoo-padding-9 {
  padding: 9px; }

.yoo-padding-10 {
  padding: 10px; }

.yoo-padding-11 {
  padding: 11px; }

.yoo-padding-12 {
  padding: 12px; }

.yoo-padding-13 {
  padding: 13px; }

.yoo-padding-14 {
  padding: 14px; }

.yoo-padding-15 {
  padding: 15px; }

.yoo-padding-top-0 {
  padding-top: 0; }

.yoo-padding-top-1 {
  padding-top: 1px; }

.yoo-padding-top-2 {
  padding-top: 2px; }

.yoo-padding-top-3 {
  padding-top: 3px; }

.yoo-padding-top-4 {
  padding-top: 4px; }

.yoo-padding-top-5 {
  padding-top: 5px; }

.yoo-padding-top-6 {
  padding-top: 6px; }

.yoo-padding-top-7 {
  padding-top: 7px; }

.yoo-padding-top-8 {
  padding-top: 8px; }

.yoo-padding-top-9 {
  padding-top: 9px; }

.yoo-padding-top-10 {
  padding-top: 10px; }

.yoo-padding-top-11 {
  padding-top: 11px; }

.yoo-padding-top-12 {
  padding-top: 12px; }

.yoo-padding-top-13 {
  padding-top: 13px; }

.yoo-padding-top-14 {
  padding-top: 14px; }

.yoo-padding-top-15 {
  padding-top: 15px; }

.yoo-padding-bottom-0 {
  padding-bottom: 0; }

.yoo-padding-bottom-1 {
  padding-bottom: 1px; }

.yoo-padding-bottom-2 {
  padding-bottom: 2px; }

.yoo-padding-bottom-3 {
  padding-bottom: 3px; }

.yoo-padding-bottom-4 {
  padding-bottom: 4px; }

.yoo-padding-bottom-5 {
  padding-bottom: 5px; }

.yoo-padding-bottom-6 {
  padding-bottom: 6px; }

.yoo-padding-bottom-7 {
  padding-bottom: 7px; }

.yoo-padding-bottom-8 {
  padding-bottom: 8px; }

.yoo-padding-bottom-9 {
  padding-bottom: 9px; }

.yoo-padding-bottom-10 {
  padding-bottom: 10px; }

.yoo-padding-bottom-11 {
  padding-bottom: 11px; }

.yoo-padding-bottom-12 {
  padding-bottom: 12px; }

.yoo-padding-bottom-13 {
  padding-bottom: 13px; }

.yoo-padding-bottom-14 {
  padding-bottom: 14px; }

.yoo-padding-bottom-15 {
  padding-bottom: 15px; }

.yoo-padding-left-0 {
  padding-left: 0; }

.yoo-padding-left-1 {
  padding-left: 1px; }

.yoo-padding-left-2 {
  padding-left: 2px; }

.yoo-padding-left-3 {
  padding-left: 3px; }

.yoo-padding-left-4 {
  padding-left: 4px; }

.yoo-padding-left-5 {
  padding-left: 5px; }

.yoo-padding-left-6 {
  padding-left: 6px; }

.yoo-padding-left-7 {
  padding-left: 7px; }

.yoo-padding-left-8 {
  padding-left: 8px; }

.yoo-padding-left-9 {
  padding-left: 9px; }

.yoo-padding-left-10 {
  padding-left: 10px; }

.yoo-padding-left-11 {
  padding-left: 11px; }

.yoo-padding-left-12 {
  padding-left: 12px; }

.yoo-padding-left-13 {
  padding-left: 13px; }

.yoo-padding-left-14 {
  padding-left: 14px; }

.yoo-padding-left-15 {
  padding-left: 15px; }

.yoo-padding-right-0 {
  padding-right: 0; }

.yoo-padding-right-1 {
  padding-right: 1px; }

.yoo-padding-right-2 {
  padding-right: 2px; }

.yoo-padding-right-3 {
  padding-right: 3px; }

.yoo-padding-right-4 {
  padding-right: 4px; }

.yoo-padding-right-5 {
  padding-right: 5px; }

.yoo-padding-right-6 {
  padding-right: 6px; }

.yoo-padding-right-7 {
  padding-right: 7px; }

.yoo-padding-right-8 {
  padding-right: 8px; }

.yoo-padding-right-9 {
  padding-right: 9px; }

.yoo-padding-right-10 {
  padding-right: 10px; }

.yoo-padding-right-11 {
  padding-right: 11px; }

.yoo-padding-right-12 {
  padding-right: 12px; }

.yoo-padding-right-13 {
  padding-right: 13px; }

.yoo-padding-right-14 {
  padding-right: 14px; }

.yoo-padding-right-15 {
  padding-right: 15px; }

/* End Padidng Classes */
.yoo-text-transform-u {
  text-transform: uppercase; }

.yoo-text-transform-l {
  text-transform: lowercase; }

.yoo-text-transform-c {
  text-transform: capitalize; }

.yoo-font-style-i {
  font-style: italic; }

.yoo-font-light {
  font-weight: 300; }

.yoo-font-regular {
  font-weight: 400; }

.yoo-font-medium {
  font-weight: 500; }

.yoo-font-semi-bold {
  font-weight: 600; }

.yoo-font-bold {
  font-weight: 700; }

.yoo-font-black {
  font-weight: 900; }

.yoo-radious1 {
  border-radius: 1px; }

.yoo-radious2 {
  border-radius: 2px; }

.yoo-radious3 {
  border-radius: 3px; }

.yoo-radious4 {
  border-radius: 4px; }

.yoo-radious5 {
  border-radius: 5px; }

.yoo-radious6 {
  border-radius: 6px; }

.yoo-radious7 {
  border-radius: 7px; }

.yoo-radious8 {
  border-radius: 8px; }

.yoo-radious9 {
  border-radius: 9px; }

.yoo-radious10 {
  border-radius: 10px; }

.yoo-radious20 {
  border-radius: 20px; }

.yoo-radious50 {
  border-radius: 50%;
  overflow: hidden; }

.yoo-padding-lr30 {
  padding-left: 30px;
  padding-right: 30px; }

.yoo-mp0 {
  margin: 0;
  padding: 0;
  list-style: none; }

.yoo-un-list {
  padding-left: 0;
  list-style: disc;
  list-style-position: inside;
  margin-bottom: 0; }
  .yoo-un-list li {
    font-size: 16px;
    line-height: 1.6em;
    margin-top: 10px; }
  .yoo-un-list ul {
    padding-left: 23px;
    list-style: disc;
    list-style-position: inside; }

.yoo-un-list > li:first-child {
  margin-top: 0; }

ol.yoo-un-list {
  list-style-type: decimal; }

.yoo-blue-box {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff; }

.yoo-gray-box {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93; }

.yoo-light-blue-box {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5ac8fa; }

.yoo-green-box {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759; }

.yoo-red-box {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30; }

.yoo-orange-box {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500; }

.yoo-indigo-box {
  background-color: rgba(88, 86, 214, 0.1);
  color: #5856d6; }

.yoo-purple-box {
  background-color: rgba(175, 82, 222, 0.1);
  color: #af52de; }

.yoo-pink-box {
  background-color: rgba(255, 45, 85, 0.1);
  color: #ff2d55; }

.yoo-box-colo1,
.yoo-box-colo2,
.yoo-box-colo3,
.yoo-box-colo4,
.yoo-box-colo5,
.yoo-box-colo6,
.yoo-box-colo7,
.yoo-box-colo8 {
  color: #fff; }

.yoo-box-colo1 {
  background-color: #007aff; }

.yoo-box-colo2 {
  background-color: #8e8e93; }

.yoo-box-colo3 {
  background-color: #5ac8fa; }

.yoo-box-colo4 {
  background-color: #34c759; }

.yoo-box-colo5 {
  background-color: #ff3b30; }

.yoo-box-colo6 {
  background-color: #ff9500; }

.yoo-box-colo7 {
  background-color: #5856d6; }

.yoo-box-colo8 {
  background-color: #8e8e93; }

.yoo-box-transparent-colo1 {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff; }

a.yoo-box-transparent-colo1:hover {
  background-color: #007aff;
  color: #fff; }

.yoo-box-transparent-colo2 {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93; }

a.yoo-box-transparent-colo2:hover {
  background-color: #8e8e93;
  color: #fff; }

.yoo-box-transparent-colo3 {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5ac8fa; }

a.yoo-box-transparent-colo3:hover {
  background-color: #5ac8fa;
  color: #fff; }

.yoo-box-transparent-colo4 {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759; }

a.yoo-box-transparent-colo4:hover {
  background-color: #34c759;
  color: #fff; }

.yoo-box-transparent-colo5 {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30; }

a.yoo-box-transparent-colo5:hover {
  background-color: #ff3b30;
  color: #fff; }

.yoo-box-transparent-colo6 {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500; }

a.yoo-box-transparent-colo6:hover {
  background-color: #ff9500;
  color: #fff; }

.yoo-box-transparent-colo7 {
  background-color: rgba(255, 149, 0, 0.1);
  color: #5856d6; }

a.yoo-box-transparent-colo7:hover {
  background-color: #5856d6;
  color: #fff; }

.yoo-box-transparent-colo8 {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93; }

a.yoo-box-transparent-colo8:hover {
  background-color: #ff9500;
  color: #fff; }

.yoo-success-color {
  color: #5ac8fa; }

.yoo-success-color-bg {
  background-color: #5ac8fa;
  color: rgba(255, 255, 255, 0.9); }

.yoo-danger-color {
  color: #34c759; }

.yoo-color-plate-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-color-plate-wrap.yoo-style1 {
    color: #fff; }
  .yoo-color-plate-wrap.yoo-style2 div {
    border: 1px solid #e5e5e5; }
  .yoo-color-plate-wrap div {
    padding: 12px 20px;
    min-width: 150px;
    margin-bottom: 10px;
    margin-right: 10px;
    text-align: center;
    border-radius: 4px; }

.yoo-blue-bg {
  background-color: #007aff !important; }

.yoo-gray-bg {
  background-color: #8e8e93 !important; }

.yoo-light-blue-bg {
  background-color: #5ac8fa !important; }

.yoo-green-bg {
  background-color: #34c759 !important; }

.yoo-red-bg {
  background-color: #ff3b30 !important; }

.yoo-orange-bg {
  background-color: #ff9500 !important; }

.yoo-indigo-bg {
  background-color: #5856d6 !important; }

.yoo-purple-bg {
  background-color: #af52de !important; }

.yoo-pink-bg {
  background-color: #ff2d55 !important; }

.yoo-blue-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(0, 122, 255, 0.2);
          box-shadow: 0 15px 20px rgba(0, 122, 255, 0.2); }

.yoo-gray-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(142, 142, 147, 0.2);
          box-shadow: 0 15px 20px rgba(142, 142, 147, 0.2); }

.yoo-light-blue-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(90, 200, 250, 0.2);
          box-shadow: 0 15px 20px rgba(90, 200, 250, 0.2); }

.yoo-green-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(52, 199, 89, 0.2);
          box-shadow: 0 15px 20px rgba(52, 199, 89, 0.2); }

.yoo-red-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(255, 59, 48, 0.2);
          box-shadow: 0 15px 20px rgba(255, 59, 48, 0.2); }

.yoo-accent-gradient6 {
  -webkit-box-shadow: 0 15px 20px rgba(255, 149, 0, 0.2);
          box-shadow: 0 15px 20px rgba(255, 149, 0, 0.2); }

.yoo-indigo-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(88, 86, 214, 0.2);
          box-shadow: 0 15px 20px rgba(88, 86, 214, 0.2); }

.yoo-purple-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(175, 82, 222, 0.2);
          box-shadow: 0 15px 20px rgba(175, 82, 222, 0.2); }

.yoo-pink-shadow {
  -webkit-box-shadow: 0 15px 20px rgba(255, 45, 85, 0.2);
          box-shadow: 0 15px 20px rgba(255, 45, 85, 0.2); }

.yoo-blue-color {
  color: #007aff !important; }

.yoo-gray-color {
  color: #8e8e93 !important; }

.yoo-light-blue-color {
  color: #5ac8fa !important; }

.yoo-green-color {
  color: #34c759 !important; }

.yoo-red-color {
  color: #ff3b30 !important; }

.yoo-orange-color {
  color: #ff9500 !important; }

.yoo-indigo-color {
  color: #5856d6 !important; }

.yoo-purple-color {
  color: #af52de !important; }

.yoo-pink-color {
  color: #ff2d55 !important; }

.yoo-base-bg1 {
  background-color: rgba(0, 0, 0, 0.95); }

.yoo-base-bg2 {
  background-color: rgba(0, 0, 0, 0.7); }

.yoo-base-bg3 {
  background-color: rgba(0, 0, 0, 0.4); }

.yoo-base-bg4 {
  background-color: #e5e5e5; }

.yoo-base-bg5 {
  background-color: rgba(0, 0, 0, 0.05); }

.yoo-base-bg6 {
  background-color: rgba(0, 0, 0, 0.02); }

.yoo-base-color1 {
  color: rgba(0, 0, 0, 0.95); }

.yoo-base-color2 {
  color: rgba(0, 0, 0, 0.7); }

.yoo-base-color3 {
  color: rgba(0, 0, 0, 0.4); }

.yoo-base-color4 {
  color: #e5e5e5; }

.yoo-base-color5 {
  color: rgba(0, 0, 0, 0.05); }

.yoo-base-color6 {
  color: rgba(0, 0, 0, 0.02); }

.yoo-white-c {
  color: #fff; }

.yoo-white-c1 {
  color: rgba(255, 255, 255, 0.1); }

.yoo-white-c2 {
  color: rgba(255, 255, 255, 0.2); }

.yoo-white-c3 {
  color: rgba(255, 255, 255, 0.3); }

.yoo-white-c4 {
  color: rgba(255, 255, 255, 0.4); }

.yoo-white-c5 {
  color: rgba(255, 255, 255, 0.5); }

.yoo-white-c6 {
  color: rgba(255, 255, 255, 0.6); }

.yoo-white-c7 {
  color: rgba(255, 255, 255, 0.7); }

.yoo-white-c8 {
  color: rgba(255, 255, 255, 0.8); }

.yoo-white-c9 {
  color: rgba(255, 255, 255, 0.9); }

.yoo-white-bg {
  background-color: #fff; }

.yoo-gray-bg {
  background-color: rgba(0, 0, 0, 0.05); }

.yoo-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }

.yoo-height-100p {
  height: 100%; }

.yoo-box-50 {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }
  .yoo-box-50 img {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover; }

.yoo-padd-lr-30 {
  padding-left: 30px;
  padding-right: 30px; }

.yoo-padd-lr-20 {
  padding-left: 20px;
  padding-right: 20px; }

.yoo-padd-lr-15 {
  padding-left: 15px;
  padding-right: 15px; }

.yoo-content.yoo-style1 {
  padding-left: 300px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  min-height: calc(100vh - 60px);
  overflow: hidden; }

.yoo-content.yoo-style2 {
  padding-left: 300px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-content.yoo-style3 {
  padding-right: 340px; }

.yoo-content.yoo-style4 {
  background-color: #fff;
  z-index: 10;
  overflow-x: hidden; }
  .yoo-content.yoo-style4 .container-fluid {
    padding: 0 70px; }

.yoo-content.yoo-style1.yoo-sp-content-container {
  min-height: calc(100vh - 103px); }

.material-icons {
  font-size: inherit; }

.yoo-card-content-height1 {
  height: 419px;
  overflow: auto; }

.yoo-opacity9 {
  opacity: 0.9; }

.yoo-opacity8 {
  opacity: 0.8; }

.yoo-opacity7 {
  opacity: 0.7; }

.yoo-opacity6 {
  opacity: 0.6; }

.yoo-opacity5 {
  opacity: 0.5; }

.yoo-opacity4 {
  opacity: 0.4; }

.yoo-opacity3 {
  opacity: 0.3; }

.yoo-opacity2 {
  opacity: 0.2; }

.yoo-opacity1 {
  opacity: 1; }

.yoo-navigation.yoo-style1 {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 4px;
  border: 1px solid #e5e5e5; }
  .yoo-navigation.yoo-style1 .yoo-prev,
  .yoo-navigation.yoo-style1 .yoo-next {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 18px;
    height: 30px;
    width: 30px; }
    .yoo-navigation.yoo-style1 .yoo-prev:hover,
    .yoo-navigation.yoo-style1 .yoo-next:hover {
      background-color: rgba(0, 0, 0, 0.05); }
  .yoo-navigation.yoo-style1 .yoo-prev {
    border-right: 1px solid #e5e5e5; }

.yoo-get-star {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 24px;
  position: relative;
  cursor: pointer;
  height: 24px;
  width: 24px;
  color: rgba(0, 0, 0, 0.4); }
  .yoo-get-star i,
  .yoo-get-star .hydrated {
    display: inline-block;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-get-star i:first-child,
    .yoo-get-star .hydrated:first-child {
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0; }
  .yoo-get-star.active i:first-child,
  .yoo-get-star.active .hydrated:first-child {
    opacity: 1; }
  .yoo-get-star.active i:last-child,
  .yoo-get-star.active .hydrated:last-child {
    opacity: 0; }

.yoo-online-status {
  height: 6px;
  width: 6px;
  background-color: #b5b5b5;
  display: inline-block;
  border-radius: 50%; }

.yoo-online-status.yoo-live {
  background-color: #34c759; }

.yoo-uikits-heading {
  border-bottom: 1px solid #e5e5e5;
  padding: 12px 0; }
  .yoo-uikits-heading .yoo-uikits-title {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 0; }
  .yoo-uikits-heading.yoo-white {
    border-color: rgba(255, 255, 255, 0.1); }

.yoo-breadcamb.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: 4px; }
  .yoo-breadcamb.yoo-style1 li:not(:last-child) {
    padding-right: 13px;
    margin-right: 11px;
    position: relative; }
    .yoo-breadcamb.yoo-style1 li:not(:last-child):before {
      content: '';
      height: 7px;
      width: 7px;
      border: 2px solid rgba(0, 0, 0, 0.4);
      position: absolute;
      -webkit-transform: rotate(45deg);
              transform: rotate(45deg);
      right: -1px;
      top: 7px;
      border-left: none;
      border-bottom: none; }

.yoo-bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center; }

.yoo-overlay.yoo-style1 {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(0, 0, 0, 0.7)));
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); }

.yoo-gutter-less .row {
  margin-left: 0;
  margin-right: 0; }
  .yoo-gutter-less .row > div {
    padding: 0;
    margin-left: -1px; }

.yoo-border {
  border: 1px solid #e5e5e5; }

.yoo-custom-input-area {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative; }
  .yoo-custom-input-area .yoo-custom-input-field {
    height: 100%;
    width: 100%;
    padding: 5px 0;
    font-size: 16px;
    line-height: 1.6em; }
    .yoo-custom-input-area .yoo-custom-input-field:focus {
      outline: none; }
  .yoo-custom-input-area [contentEditable=true]:empty:not(:focus):before {
    content: attr(data-placeholder);
    color: rgba(0, 0, 0, 0.4); }

.yoo-mobile-toggle-btn {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.4);
  display: none; }

.yoo-with-mobile-toggle {
  position: relative; }

.nicescroll-rails div {
  background-color: #e5e5e5 !important; }

@media screen and (max-width: 1199px) {
  .yoo-content.yoo-style2 {
    padding-left: 0px; }

  .yoo-mobile-toggle-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; } }
@media screen and (max-width: 575px) {
  .yoo-hide-mobile {
    display: none; } }
.yoo-chart-tooltip {
  pointer-events: none;
  position: absolute;
  font-size: 13px;
  text-align: center;
  background: white;
  padding: 3px 12px 4px;
  z-index: 5;
  margin: 0 auto;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  display: none;
  z-index: 1000;
  color: rgba(0, 0, 0, 0.7);
  background-color: #fff; }
  .yoo-chart-tooltip.active {
    display: block; }
  .yoo-chart-tooltip:after {
    content: "";
    position: absolute;
    margin-left: -5px;
    height: 10px;
    width: 10px;
    border: 1px solid #e5e5e5;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    left: 50%;
    bottom: -6px;
    background-color: #fff;
    border-left: 0;
    border-top: 0; }

/*Custome Sticky*/
.tt-sticky-content {
  position: relative; }

.tt-active-sticky .tt-sticky-content-in {
  position: fixed;
  bottom: 0px; }

.tt-active-sticky-sm .tt-sticky-content-in {
  position: fixed;
  top: 0px; }

.tt-active-sticky.tt-active-absoulut .tt-sticky-content-in {
  position: absolute;
  top: initial;
  left: 0 !important; }

.tt-active-sticky-sm.tt-active-absoulut-bal .tt-sticky-content-in {
  position: absolute;
  bottom: 0;
  top: initial;
  left: 0 !important; }

.yoo-card-settings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4); }
  .yoo-card-settings button {
    border: none;
    background-color: transparent;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0 4px;
    color: inherit;
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-card-settings button:focus {
      outline: none; }
    .yoo-card-settings button:hover {
      color: rgba(0, 0, 0, 0.7); }

.yoo-dragable-card-toggle.yoo-active {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg); }

.yoo-card-heading .yoo-card-settings {
  margin-right: -8px; }

.yoo-draggable-card-wrap .yoo-card:not(:last-child) {
  margin-bottom: 30px; }

.yoo-icons-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-icons-wrap li {
    width: 16.666667%;
    padding: 0 15px; }
  .yoo-icons-wrap .yoo-icons-wrap-in {
    text-align: center;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-bottom: 30px;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-icons-wrap .yoo-icons-wrap-in:hover {
      -webkit-transform: translateY(-6px);
              transform: translateY(-6px); }
  .yoo-icons-wrap i,
  .yoo-icons-wrap .hydrated {
    display: block;
    font-size: 40px;
    padding: 30px 0; }
  .yoo-icons-wrap .yoo-icons-classes {
    font-size: 14px;
    border-top: 1px solid #e5e5e5;
    display: block;
    padding: 10px 0; }

/* Ensure that the demo table scrolls */
.yoo-data-table th,
.yoo-data-table td {
  white-space: nowrap; }
.yoo-data-table .dataTables_wrapper {
  width: 100%;
  margin: 0 auto; }

.yoo-badge-group.yoo-style1 .yoo-badge {
  margin-right: 10px; }

.yoo-badge {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0 5px;
  border-radius: 2px;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 500;
  line-height: 17px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

a.yoo-badge:hover {
  color: #fff;
  opacity: 0.8; }

.yoo-section-heading.yoo-style1 .yoo-section-title {
  font-size: 50px;
  margin-bottom: 1px; }
.yoo-section-heading.yoo-style1 .yoo-section-subtitle {
  font-size: 21px;
  line-height: 1.6em; }

.yoo-section-heading.yoo-style2 .yoo-section-title {
  font-size: 70px;
  margin-bottom: 2px; }
.yoo-section-heading.yoo-style2 .yoo-section-subtitle {
  font-size: 21px;
  line-height: 1.6em; }
.yoo-section-heading.yoo-style2.yoo-white .yoo-section-title {
  color: #fff; }
.yoo-section-heading.yoo-style2.yoo-white .yoo-section-subtitle {
  color: rgba(255, 255, 255, 0.7); }

.yoo-landing-testimonial {
  border: 2px solid #eaeaea;
  padding: 30px 40px;
  padding-bottom: 40px;
  border-radius: 10px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-landing-testimonial:hover {
    -webkit-transform: translateY(-5px);
            transform: translateY(-5px);
    -webkit-box-shadow: 4.386px 14.345px 25.5px 4.5px rgba(17, 17, 18, 0.1);
            box-shadow: 4.386px 14.345px 25.5px 4.5px rgba(17, 17, 18, 0.1); }
  .yoo-landing-testimonial .yoo-landing-testimonial-text {
    font-size: 21px;
    line-height: 1.6em;
    margin-bottom: 18px; }
  .yoo-landing-testimonial .yoo-landing-testimonial-meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }
  .yoo-landing-testimonial .yoo-landing-testimonial-img {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    margin-right: 8px; }
  .yoo-landing-testimonial .yoo-landing-testimonial-name {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 0; }
  .yoo-landing-testimonial .yoo-landing-testimonial-desc {
    font-size: 16px;
    line-height: 1.6em; }

.yoo-side-footer {
  background-color: #101010;
  overflow: hidden; }
  .yoo-side-footer .yoo-footer-img {
    width: calc(100% + 200px);
    position: relative;
    left: 50%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    margin-bottom: -60px; }

.yoo-footer-btn {
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 0.4em 2.4em;
  font-size: 16px;
  line-height: 1.6em; }
  .yoo-footer-btn:hover {
    color: #101010;
    background-color: #fff;
    border-color: #fff;
    -webkit-transform: translateY(-1px);
            transform: translateY(-1px); }

.yoo-dropdown {
  list-style: none;
  position: absolute;
  width: 280px;
  background: #fff;
  left: 0;
  top: 100%;
  padding: 10px 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  border-radius: 4px;
  display: block;
  z-index: 6;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 .yoo-has-children > a {
  position: relative; }

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 .yoo-has-children > a:before {
  content: '';
  position: absolute;
  height: 6px;
  width: 6px;
  border: 1px solid rgba(0, 0, 0, 0.4);
  border-left: 0;
  border-top: 0;
  right: 15px;
  top: 15px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg); }

.yoo-drop-style1 .yoo-toggle-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 24px;
  padding: 5px 0; }
  .yoo-drop-style1 .yoo-toggle-btn.yoo-large-size {
    font-size: 30px; }
  .yoo-drop-style1 .yoo-toggle-btn.yoo-small-size {
    font-size: 18px; }
.yoo-drop-style1 .yoo-drop-dropdown-list {
  padding: 0; }
.yoo-drop-style1 .btn:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none; }

.yoo-toggle-list.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-toggle-list.yoo-style1 .yoo-toggle-body {
    margin-right: 20px; }
    .yoo-toggle-list.yoo-style1 .yoo-toggle-body:last-child {
      margin-right: 0; }

.yoo-drop-style1 .yoo-toggle-btn {
  cursor: pointer; }
.yoo-drop-style1 .yoo-dropdown {
  margin-top: 10px; }
.yoo-drop-style1 .yoo-dropdown.active {
  margin-top: 0;
  opacity: 1;
  visibility: visible; }

.yoo-left-dropdown .yoo-dropdown {
  left: 0; }

.yoo-right-dropdown .yoo-dropdown {
  left: inherit;
  right: 0; }

.custom-control-input:focus ~ .custom-control-label::before,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  -webkit-box-shadow: none;
          box-shadow: none; }

.modal-content {
  background-color: #fff; }

.modal-header,
.modal-footer {
  border-color: #e5e5e5; }

.close,
.close:hover {
  text-shadow: none;
  color: rgba(0, 0, 0, 0.7);
  opacity: 0.7; }

.close:hover {
  opacity: 1; }

.table {
  color: rgba(0, 0, 0, 0.7); }

.yoo-card-heading-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }

.badge-pill {
  border-radius: 1.6em; }

.highlight-wrap {
  padding: 30px;
  position: relative;
  background-color: #f7f7f9; }

.highlight {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 15px 20px; }
  .highlight pre {
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
    background-color: transparent;
    border: 0; }
    .highlight pre code {
      font-size: inherit;
      color: #212529; }
  .highlight .nt {
    color: #2f6f9f; }
  .highlight .na {
    color: #4f9fcf; }
  .highlight .s {
    color: #d44950; }

.bg-primary {
  background-color: #007aff !important; }

.bg-blue-gray {
  background-color: #8e8e93 !important; }

.bg-secondary {
  background-color: #8e8e93 !important; }

.bg-success {
  background-color: #34c759 !important; }

.bg-danger {
  background-color: #ff3b30 !important; }

.bg-warning {
  background-color: #ff9500 !important; }

.bg-info {
  background-color: #5ac8fa !important; }

.bg-dark {
  background-color: rgba(0, 0, 0, 0.95) !important; }

.bg-light {
  background-color: rgba(0, 0, 0, 0.05) !important; }

.bg-primary-off70 {
  background-color: rgba(0, 122, 255, 0.7) !important; }

.bg-secondary-off70 {
  background-color: rgba(142, 142, 147, 0.7) !important; }

.bg-success-off70 {
  background-color: rgba(52, 199, 89, 0.7) !important; }

.bg-danger-off70 {
  background-color: rgba(255, 59, 48, 0.7) !important; }

.bg-warning-off70 {
  background-color: rgba(255, 149, 0, 0.7) !important; }

.bg-info-off70 {
  background-color: rgba(90, 200, 250, 0.7) !important; }

.bg-dark-off70 {
  background-color: rgba(0, 0, 0, 0.7) !important; }

.bg-light-off70 {
  background-color: rgba(0, 0, 0, 0.02) !important; }

.border-primary {
  border-color: #007aff !important; }

.border-blue-gray {
  border-color: #8e8e93 !important; }

.border-secondary {
  border-color: #5856d6 !important; }

.border-success {
  border-color: #5ac8fa !important; }

.border-danger {
  border-color: #34c759 !important; }

.border-warning {
  border-color: #ff3b30 !important; }

.border-info {
  border-color: #8e8e93 !important; }

.border-dark {
  border-color: rgba(0, 0, 0, 0.95) !important; }

.border-light {
  border-color: #e5e5e5 !important; }

.text-primary,
.text-primary .card-title {
  color: #007aff !important; }

.text-blue-gray,
.text-blue-gray .card-title {
  color: #8e8e93 !important; }

.text-secondary,
.text-secondary .card-title {
  color: #8e8e93 !important; }

.text-success,
.text-success .card-title {
  color: #5ac8fa !important; }

.text-danger,
.text-danger .card-title {
  color: #ff3b30 !important; }

.text-warning,
.text-warning .card-title {
  color: #ff9500 !important; }

.text-info,
.text-info .card-title {
  color: #5ac8fa !important; }

.text-dark,
.text-dark .card-title {
  color: rgba(0, 0, 0, 0.95) !important; }

.blockquote {
  font-size: 20px;
  line-height: 1.6em;
  border-left: 4px solid #e5e5e5;
  padding-left: 20px;
  padding-top: 6px;
  padding-bottom: 6px;
  margin: 0; }

.blockquote.text-right {
  border-left: none;
  border-right: 4px solid #e5e5e5;
  padding-left: 0;
  padding-right: 20px; }

.blockquote.text-center {
  border-left: none;
  padding-left: 0; }

.yoo-vertical-middle {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%; }
  .yoo-vertical-middle .yoo-vertical-middle-in {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    width: 100%; }

.display-1 {
  font-size: 96px; }

.display-2 {
  font-size: 88px; }

.display-3 {
  font-size: 79px; }

.display-4 {
  font-size: 61px; }

.blockquote-footer {
  color: rgba(0, 0, 0, 0.4);
  font-size: inherit; }

.figure-caption {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.4); }

.figure-img {
  margin-bottom: 5px; }

.accordion .card {
  border-radius: 4px !important; }
.accordion .card-header {
  padding: 0;
  background-color: transparent;
  margin-bottom: 0 !important; }
.accordion .card-body {
  padding: 15px 15px;
  border-bottom: 1px solid #e5e5e5; }
.accordion .btn {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 13px 20px;
  display: block;
  width: 100%;
  text-align: inherit;
  font-size: 16px;
  text-transform: initial;
  font-weight: 400;
  color: #007aff;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px 4px 0 0;
  box-shadow: none !important; }
  .accordion .btn.collapsed {
    color: #007aff;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px; }
  .accordion .btn:hover, .accordion .btn:focus {
    text-decoration: none;
    color: rgba(0, 122, 255, 0.8); }
.accordion .card:not(:last-child) {
  margin-bottom: 10px; }

.close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.rounded-sm {
  border-radius: 4px !important; }

.rounded-lg {
  border-radius: 10px !important; }

.bd-example-row .row > .col,
.bd-example-row .row > [class^=col-] {
  padding-top: .75rem;
  padding-bottom: .75rem;
  background-color: rgba(86, 61, 124, 0.15);
  border: 1px solid rgba(86, 61, 124, 0.2); }

.bd-example-row .row + .row {
  margin-top: 1rem; }

code {
  color: #ff3b30; }

.bd-example {
  border-color: rgba(0, 0, 0, 0.05); }

.bd-lead {
  font-size: 18px;
  line-height: 1.6em;
  font-weight: 400; }

.bd-example-border-utils [class^=border],
.bd-example-border-utils [class^=rounded] {
  display: inline-block;
  width: 5rem;
  height: 5rem;
  margin: .25rem;
  background-color: rgba(0, 0, 0, 0.05); }

.bd-example > .close {
  float: none; }

.shadow {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075), 0 2px 2px rgba(0, 0, 0, 0.075), 0 4px 4px rgba(0, 0, 0, 0.075), 0 6px 6px rgba(0, 0, 0, 0.075), 0 8px 8px rgba(0, 0, 0, 0.075) !important;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075), 0 2px 2px rgba(0, 0, 0, 0.075), 0 4px 4px rgba(0, 0, 0, 0.075), 0 6px 6px rgba(0, 0, 0, 0.075), 0 8px 8px rgba(0, 0, 0, 0.075) !important; }

.shadow-lg {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05), 0 2px 2px rgba(0, 0, 0, 0.05), 0 4px 4px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05), 0 8px 8px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.05), 0 12px 12px rgba(0, 0, 0, 0.05), 0 14px 14px rgba(0, 0, 0, 0.05), 0 16px 16px rgba(0, 0, 0, 0.05) !important;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05), 0 2px 2px rgba(0, 0, 0, 0.05), 0 4px 4px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05), 0 8px 8px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.05), 0 12px 12px rgba(0, 0, 0, 0.05), 0 14px 14px rgba(0, 0, 0, 0.05), 0 16px 16px rgba(0, 0, 0, 0.05) !important; }

.shadow-sm {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1), 0 2px 2px rgba(0, 0, 0, 0.1), 0 4px 4px rgba(0, 0, 0, 0.1) !important;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1), 0 2px 2px rgba(0, 0, 0, 0.1), 0 4px 4px rgba(0, 0, 0, 0.1) !important; }

.bd-highlight {
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e5e5; }

.bd-content h2 {
  font-size: 32px;
  font-weight: 500; }

.bd-content ul,
.bd-example ul {
  list-style-type: disc; }
.bd-content li,
.bd-example li {
  font-size: 15px;
  line-height: 1.6em; }

.bd-example-row .row > .col,
.bd-example-row .row > [class^=col-] {
  padding-top: .75rem;
  padding-bottom: .75rem;
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e5e5; }

kbd {
  background-color: rgba(0, 0, 0, 0.95); }

.media-body h5 {
  font-size: 21px;
  margin-bottom: 4px; }

.media-body p {
  margin-bottom: 8px; }

.yoo-box-lg {
  height: 64px;
  width: 64px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 18px;
  font-weight: 600; }
  .yoo-box-lg i,
  .yoo-box-lg .hydrated {
    font-size: 34px; }
  .yoo-box-lg img {
    height: 100%;
    width: 100%;
    border-radius: inherit;
    -o-object-fit: cover;
       object-fit: cover; }

.yoo-box-md {
  height: 48px;
  width: 48px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 16px;
  font-weight: 600; }
  .yoo-box-md i,
  .yoo-box-md .hydrated {
    font-size: 30px; }
  .yoo-box-md img {
    height: 100%;
    width: 100%;
    border-radius: inherit;
    -o-object-fit: cover;
       object-fit: cover; }

.yoo-box-sm {
  height: 30px;
  width: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 14px;
  font-weight: 600; }
  .yoo-box-sm i,
  .yoo-box-sm .hydrated {
    font-size: 22px; }
  .yoo-box-sm img {
    height: 100%;
    width: 100%;
    border-radius: inherit;
    -o-object-fit: cover;
       object-fit: cover; }

.yoo-groth-percentage {
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 22px; }
  .yoo-groth-percentage i,
  .yoo-groth-percentage .hydrated {
    font-size: 16px;
    margin-right: 2px; }

.yoo-switch {
  position: relative;
  background-color: #34c759;
  height: 22px;
  width: 37px;
  border-radius: 1.6em;
  cursor: pointer;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }
  .yoo-switch .yoo-switch-in {
    height: 18px;
    width: 18px;
    border-radius: 50%;
    background-color: #fff;
    position: absolute;
    left: 2px;
    top: 2px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease; }
  .yoo-switch.active {
    background-color: #8e8e93; }
    .yoo-switch.active .yoo-switch-in {
      left: 17px; }

.yoo-switch.yoo-style1 {
  height: 20px;
  width: 20px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  background-color: transparent; }
  .yoo-switch.yoo-style1:before {
    content: '';
    position: absolute;
    height: 14px;
    width: 14px;
    border-radius: 50%;
    background-color: #007aff;
    left: 2px;
    top: 2px;
    opacity: 0;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }
  .yoo-switch.yoo-style1.active {
    border-color: #007aff; }
    .yoo-switch.yoo-style1.active:before {
      opacity: 1;
      -webkit-transform: scale(1);
              transform: scale(1); }

.scroll-content {
  position: relative; }

.yoo-blur {
  position: absolute;
  height: 100%;
  width: 100%;
  min-height: 100vh; }
  .yoo-blur .yoo-blur-in1,
  .yoo-blur .yoo-blur-in2 {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    min-height: 100vh; }
  .yoo-blur .yoo-blur-in1 {
    z-index: 1;
    background-color: #fff;
    opacity: 0.2; }
  .yoo-blur .yoo-blur-in2 {
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.8);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 0, 0, 0.1); }

.yoo-fixed-bg {
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed; }

.yoo-documentation-body {
  font-size: 16px; }

.yoo-doc-box {
  border: 1px solid #e5e5e5;
  border-radius: 10px;
  padding: 15px 20px;
  background-color: #fff; }

.yoo-structure-list {
  padding: 0;
  margin: 0;
  list-style: none; }
  .yoo-structure-list ul {
    list-style: none;
    margin: 0;
    padding: 0 0 0 15px; }
    .yoo-structure-list ul li {
      display: block;
      position: relative; }
      .yoo-structure-list ul li:before {
        content: '|--';
        display: inline-block;
        margin-right: 5px; }

.yoo-check-mark,
.yoo-check-mark-all {
  font-size: 24px;
  height: 20px;
  width: 20px;
  border: 1px solid #DBDBDB;
  border-radius: 6px;
  position: relative;
  background-color: #fff;
  color: #b5b5b5; }
  .yoo-check-mark:before,
  .yoo-check-mark-all:before {
    content: '';
    top: 1px;
    left: 5px;
    height: 12px;
    width: 7px;
    border-radius: 0;
    border: 2px solid #fff;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    border-left: none;
    border-top: none;
    opacity: 0;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    z-index: 1;
    position: absolute; }
  .yoo-check-mark .hydrated,
  .yoo-check-mark i,
  .yoo-check-mark-all .hydrated,
  .yoo-check-mark-all i {
    position: absolute;
    left: -3px;
    top: -3px; }
  .yoo-check-mark.active,
  .yoo-check-mark-all.active {
    border-color: #007aff;
    background-color: #007aff; }
    .yoo-check-mark.active:after, .yoo-check-mark.active:before,
    .yoo-check-mark-all.active:after,
    .yoo-check-mark-all.active:before {
      opacity: 1; }
    .yoo-check-mark.active .hydrated,
    .yoo-check-mark.active i,
    .yoo-check-mark-all.active .hydrated,
    .yoo-check-mark-all.active i {
      color: #007aff; }

.yoo-check-mark-all .yoo-first,
.yoo-check-mark-all .yoo-last {
  position: absolute;
  height: calc(100% + 2px);
  width: calc(100% + 2px);
  left: -1px;
  top: -1px; }
.yoo-check-mark-all .yoo-first {
  z-index: 3; }
.yoo-check-mark-all.active .yoo-last {
  z-index: 4; }

.yoo-icon-wrap iframe {
  border: none;
  width: 100%;
  height: calc(100vh - 302px);
  min-height: 500px;
  border-radius: 10px; }

.yoo-add {
  padding: 20px;
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff; }
  .yoo-add .yoo-add-in {
    padding: 25px 15px;
    display: block;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    background-color: #f2f2f6;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    text-align: center; }
  .yoo-add .yoo-add-thumb {
    display: block;
    -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    width: 160px;
    margin: auto;
    margin-bottom: 16px; }
  .yoo-add .yoo-add-title {
    font-size: 16px;
    margin: 0;
    font-weight: 500;
    margin-bottom: 16px; }

.yoo-sidebar-active .yoo-add {
  display: none; }
.yoo-sidebar-active .yoo-add + .yoo-sidebarheader-in {
  height: 100% !important; }

pre {
  margin: 0;
  border: 1px solid #e5e5e5;
  border-radius: 7px;
  padding: 8px 15px;
  background: #fff; }

@media screen and (min-width: 991px) {
  .yoo-add + .yoo-sidebarheader-in {
    height: calc(100% - 260px) !important; } }
@media screen and (max-width: 991px) {
  .yoo-add {
    display: none; } }
@media screen and (max-width: 991px) {
  .tt-sticky-content-in {
    width: 100% !important;
    left: 0 !important;
    position: initial !important; }

  .tt-sticky-content-middle,
  .tt-sticky-content {
    height: initial !important; }

  .yoo-card-content-height1 {
    height: initial; }

  .yoo-icons-wrap li {
    width: 33.333333%; }

  .container-fluid {
    padding-left: 15px;
    padding-right: 15px; } }
@media screen and (max-width: 575px) {
  .yoo-icons-wrap li {
    width: 50%; } }
@media screen and (max-width: 400px) {
  .yoo-icons-wrap li {
    width: 100%; } }
/*========== End General Style  ==========*/
/*========== Start Footer  ==========*/
.yoo-footer.yoo-style1 .yoo-footer-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-top: 1px solid #e5e5e5;
  padding: 14px 0; }

.yoo-footer-nav li:not(:last-child) {
  margin-right: 30px; }
.yoo-footer-nav a {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7); }
  .yoo-footer-nav a:hover {
    color: #007aff; }

.yoo-copyride {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.6em; }
  .yoo-copyride a {
    color: #007aff; }
    .yoo-copyride a:hover {
      color: rgba(0, 122, 255, 0.8); }

.yoo-sticky-footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  padding-left: 370px;
  background-color: #fff;
  z-index: 1; }
  .yoo-sticky-footer .container {
    position: relative; }
  .yoo-sticky-footer:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.05); }

.yoo-footer.yoo-style1.yoo-white .yoo-copyride,
.yoo-footer.yoo-style1.yoo-white .yoo-footer-nav a {
  color: rgba(255, 255, 255, 0.7); }
.yoo-footer.yoo-style1.yoo-white .yoo-footer-content {
  border-color: rgba(255, 255, 255, 0.1); }

@media screen and (max-width: 1199px) {
  .yoo-sticky-footer {
    padding-left: 0; } }
@media screen and (max-width: 991px) {
  .yoo-footer.yoo-style1 .yoo-footer-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center; }

  .yoo-sticky-footer {
    position: initial; } }
/*========== End Footer  ==========*/
/*========== Start Smooth Scrollbar Style  ==========*/
scrollbar,
[scrollbar],
[data-scrollbar] {
  display: block;
  position: relative;
  overflow: scroll; }

scrollbar .scroll-content,
[scrollbar] .scroll-content,
[data-scrollbar] .scroll-content {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  will-change: transform; }

scrollbar.scrolling .scroll-content,
[scrollbar].scrolling .scroll-content,
[data-scrollbar].scrolling .scroll-content {
  pointer-events: none; }

scrollbar.scrolling .scroll-content .scroll-content,
[scrollbar].scrolling .scroll-content .scroll-content,
[data-scrollbar].scrolling .scroll-content .scroll-content {
  pointer-events: auto; }

scrollbar .scrollbar-track,
[scrollbar] .scrollbar-track,
[data-scrollbar] .scrollbar-track {
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 0.5s 1s ease-out, background 0.5s ease-out;
  transition: opacity 0.5s 1s ease-out, background 0.5s ease-out;
  background: none; }

scrollbar .scrollbar-track.show,
[scrollbar] .scrollbar-track.show,
[data-scrollbar] .scrollbar-track.show,
scrollbar .scrollbar-track:hover,
[scrollbar] .scrollbar-track:hover,
[data-scrollbar] .scrollbar-track:hover {
  opacity: 1;
  -webkit-transition-delay: 0s;
  transition-delay: 0s; }

scrollbar .scrollbar-track:hover,
[scrollbar] .scrollbar-track:hover,
[data-scrollbar] .scrollbar-track:hover {
  background: transparent; }

scrollbar .scrollbar-track-x,
[scrollbar] .scrollbar-track-x,
[data-scrollbar] .scrollbar-track-x {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px; }

scrollbar .scrollbar-track-y,
[scrollbar] .scrollbar-track-y,
[data-scrollbar] .scrollbar-track-y {
  top: 0;
  right: 0;
  width: 6px;
  height: 100%; }

scrollbar .scrollbar-thumb,
[scrollbar] .scrollbar-thumb,
[data-scrollbar] .scrollbar-thumb {
  position: absolute;
  top: 0;
  left: -5px;
  width: 6px;
  height: 8px;
  background: transparent;
  border-radius: 4px; }

[data-scrollbar] .scrollbar-thumb:before,
[data-scrollbar] .scrollbar-thumb:after {
  content: '';
  position: absolute;
  height: calc(100% - 40px);
  width: 6px;
  background: #e5e5e5;
  top: 20px;
  right: 0;
  border-radius: 4px; }

[data-scrollbar] .scrollbar-thumb:before {
  z-index: 1; }

[data-scrollbar] .scrollbar-thumb:after {
  background-color: #fff; }

/*========== End Smooth Scrollbar Style  ==========*/
/*========== Start Header  ==========*/
.yoo-side-heading {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%; }

.yoo-header {
  position: relative;
  z-index: 101; }

.yoo-header.yoo-sticky-menu {
  position: fixed;
  width: 100%;
  top: 0;
  background: #272c2b;
  z-index: 100; }

.yoo-header.yoo-style1 .yoo-main-header {
  position: relative; }

.yoo-header.yoo-style1 .yoo-main-header-in {
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-header.yoo-style2 {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5; }

/*Style2*/
.yoo-header.yoo-style1 .yoo-main-header {
  position: relative; }

.yoo-header.yoo-style2 .yoo-main-header-in {
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

/*=== Logo Area ===*/
.yoo-site-title {
  font-size: 20px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.95); }

.yoo-site-title a {
  color: inherit; }

.yoo-logo-link img {
  display: block;
  margin-left:30px;
  height: 32px; }


/*End Logo Area*/
/*Horizontal Menu Area*/
.yoo-nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-nav-wrap,
.yoo-desktop-nav.yoo-nav,
.yoo-desktop-nav .yoo-nav-list {
  height: 100%; }

.yoo-nav-list > li > a > i,
.yoo-nav-list > li > a > .hydrated {
  color: rgba(255, 255, 255, 0.54);
  font-size: 24px;
  margin-right: 8px; }

.yoo-nav.yoo-desktop-nav {
  display: block !important; }

.yoo-desktop-nav .yoo-nav-list {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  list-style: none; }

.yoo-desktop-nav .yoo-nav-list > li {
  margin-left: 35px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-desktop-nav .yoo-nav-list > li:first-child {
  margin-left: 0; }

.yoo-desktop-nav .yoo-nav-list > li > a {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 8px 0px;
  border-radius: 4px 4px 0 0; }

.yoo-desktop-nav .yoo-nav-list > li:hover > a {
  background-color: rgba(0, 0, 0, 0.02); }

.yoo-nav-list > li > a {
  color: rgba(255, 255, 255, 0.87);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0; }

.yoo-nav-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 9px;
  font-weight: 500;
  line-height: 1.7em;
  padding: 0 4px;
  border-radius: 2px;
  display: inline-block; }

.yoo-nav-label.yoo-style1 {
  position: relative;
  top: -6px;
  margin-left: 5px; }

.yoo-nav-label.yoo-color1 {
  background: #34c759; }

.yoo-nav-label.yoo-color2 {
  background: #5ac8fa; }

.yoo-nav-label.yoo-style2 {
  background-color: #5ac8fa;
  margin-left: auto; }

/*=== Horizontal Dropdown ===*/
.yoo-has-children {
  position: relative; }

.yoo-desktop-nav .yoo-nav-list > .yoo-has-children > a {
  position: relative; }

.yoo-desktop-nav .yoo-nav-list > li > a.active {
  color: rgba(0, 0, 0, 0.4); }

.yoo-desktop-nav .yoo-nav-list > .yoo-has-children > a:before {
  border-left: 0;
  border-top: 0; }

.yoo-desktop-nav .yoo-has-children .yoo-has-children > a:before {
  border-left: 0;
  border-bottom: 0;
  right: 18px;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg); }

.yoo-nav .yoo-dropdown {
  border-radius: 4px; }

.yoo-nav-list ul a,
.yoo-profile-nav .yoo-dropdown a {
  color: rgba(0, 0, 0, 0.95);
  padding: 9px 20px;
  display: block;
  font-size: 14px; }

.yoo-desktop-nav .yoo-nav-list .yoo-sub-megamenu-list li a:hover {
  background: transparent; }

.yoo-desktop-nav .yoo-nav-list ul a:hover,
.yoo-desktop-nav .yoo-nav-list .yoo-sub-megamenu-list li.active a,
.yoo-profile-nav .yoo-dropdown a:hover {
  background-color: rgba(0, 0, 0, 0.05); }

.yoo-desktop-nav .yoo-nav-list .yoo-has-children > ul ul {
  left: 100%;
  top: 0; }

.yoo-desktop-nav .yoo-has-children:hover > ul {
  opacity: 1;
  visibility: visible;
  display: block !important; }

.yoo-nav-list ul .yoo-has-children > a {
  padding-right: 25px; }

.yoo-dropdown.yoo-style1 li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-dropdown.yoo-style1 li i,
.yoo-dropdown.yoo-style1 li .hydrated {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.4);
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  margin-right: 8px; }

/*Style 2,3,4*/
.yoo-dropdown.yoo-style2 li,
.yoo-dropdown.yoo-style3 li,
.yoo-dropdown.yoo-style4 li,
.yoo-dropdown.yoo-style5 li {
  padding: 0 10px; }

.yoo-dropdown.yoo-style2 a,
.yoo-dropdown.yoo-style3 a,
.yoo-dropdown.yoo-style4 a {
  padding: 7px 10px;
  border-radius: 2px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-dropdown.yoo-style3 .yoo-dropdown-cta a:hover,
.yoo-dropdown.yoo-style4 .yoo-dropdown-cta a:hover {
  padding-left: 10px; }

.yoo-dropdown.yoo-style2 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style3 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style4 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style5 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style6 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style7 .yoo-subdropdown-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.95);
  font-weight: 500;
  padding: 7px 20px 5px !important; }

.yoo-dropdown.yoo-style1 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style2 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style3 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style4 .yoo-dropdown-cta {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid #e5e5e5; }

.yoo-dropdown.yoo-style2 li i,
.yoo-dropdown.yoo-style3 li i {
  display: inline-block;
  margin-right: 8px;
  width: 13px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease; }

.yoo-dropdown.yoo-style4 li a:hover i {
  width: 9px;
  margin-right: 4px; }

/*Style 5,6,7*/
.yoo-desc-title {
  font-size: 14px;
  line-height: 1.25em;
  margin-bottom: 2px;
  color: rgba(0, 0, 0, 0.95); }

.yoo-desc-cat {
  display: block;
  color: rgba(0, 0, 0, 0.4);
  line-height: 1.25em;
  font-size: 13px; }

.yoo-dropdown.yoo-style5 a,
.yoo-dropdown.yoo-style6 a,
.yoo-dropdown.yoo-style7 a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-dropdown.yoo-style5 a {
  padding: 9px 10px; }

.yoo-dropdown.yoo-style6 a,
.yoo-dropdown.yoo-style7 a {
  padding: 10px 20px; }

.yoo-desc-box {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-right: 15px;
  font-size: 18px; }

.yoo-desc-box i {
  font-size: 21px; }

.yoo-desc-box img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover; }

/*=== Hovizontal Mega Menu ===*/
.yoo-desktop-nav .yoo-has-children.yoo-megamenu.yoo-style1 {
  position: initial; }

.yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  top: 100%;
  width: 1170px;
  background-color: #fff;
  border-radius: 4px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  opacity: 0;
  visibility: hidden;
  z-index: 2;
  margin-left: 0px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  opacity: 1;
  visibility: visible; }

.yoo-megamenu-title {
  font-size: 12px;
  line-height: 1.65em;
  margin-bottom: 0;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.4); }

.yoo-desktop-nav .yoo-megamenu-title {
  padding: 6px 20px; }

.yoo-megamenu-list {
  padding: 0;
  list-style: none; }

.yoo-megamenu.yoo-style1 .row {
  margin: 0; }

.yoo-megamenu.yoo-style1 .container,
.yoo-megamenu.yoo-style1 .row > div {
  padding: 0;
  margin-top: 0; }

.yoo-desktop-nav .yoo-nav-list .yoo-megamenu ul a {
  padding: 9px 20px; }
  .yoo-desktop-nav .yoo-nav-list .yoo-megamenu ul a:hover {
    background-color: rgba(0, 0, 0, 0.05); }

.yoo-desktop-nav .yoo-megamenu-col {
  height: 100%;
  padding: 10px 0;
  border-right: 1px solid #e5e5e5; }

.yoo-desktop-nav .row > div:last-child > .yoo-megamenu-col {
  border-right: 0; }

/*Style2 3 4*/
.yoo-megamenu.yoo-style2 .container {
  padding-left: 30px;
  padding-right: 30px; }

.yoo-megamenu.yoo-style2 .yoo-megamenu-in {
  padding-top: 15px;
  padding-bottom: 15px; }

.yoo-megamenu.yoo-style3 .row,
.yoo-megamenu.yoo-style4 .row {
  margin-top: -30px; }

.yoo-megamenu.yoo-style2 .row > div {
  padding-top: 15px;
  padding-bottom: 15px; }

.yoo-megamenu.yoo-style3 .row > div,
.yoo-megamenu.yoo-style4 .row > div {
  margin-top: 30px; }

/*Style5*/
.yoo-megamenu.yoo-style5 .row {
  position: relative; }

.yoo-megamenu.yoo-style5 .row:before {
  content: "";
  height: 1px;
  width: calc(100% + 30px);
  position: absolute;
  background: #fff;
  bottom: 0px;
  left: -15px;
  z-index: 1; }

.yoo-megamenu.yoo-style5 .row > div {
  padding-top: 30px;
  padding-bottom: 30px;
  position: relative; }

.yoo-megamenu.yoo-style5 .row > div:before {
  content: "";
  position: absolute;
  height: 1px;
  width: calc(100% + 30px);
  background-color: #e5e5e5;
  bottom: 0;
  left: -15px; }

/*Style 3, 4*/
.yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
.yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
  list-style: none;
  padding: 30px 0;
  width: 230px;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  border-right: 1px solid #e5e5e5; }

.yoo-megamenu.yoo-style3 .yoo-megamenu-in {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.yoo-megamenu-in {
  overflow: hidden; }

.yoo-megamenu.yoo-style3 .yoo-sub-megamenu,
.yoo-megamenu.yoo-style4 .yoo-sub-megamenu,
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu {
  width: 100%;
  padding: 30px 15px; }

.yoo-megamenu.yoo-style6 .yoo-megamenu-in {
  padding: 30px 15px; }

/*Style5*/
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu {
  padding-top: 0;
  padding-bottom: 0; }

/*End Horizontal Mega menu*/
/*=== Horizontal Mobile Menu Style ===*/
.yoo-mobile-nav {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  background-color: #212121;
  padding: 20px 0; }

.yoo-mobile-nav ul {
  margin: 0;
  list-style: none;
  padding: 0;
  padding-left: 15px; }

.yoo-mobile-nav .yoo-nav-list ul {
  display: none; }

.yoo-mobile-nav .yoo-nav-list > li > a {
  padding: 8px 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.yoo-mobile-nav .yoo-nav-list ul a {
  padding-left: 0;
  padding-right: 0; }

.yoo-sub-megamenu-in {
  display: none; }

.yoo-sub-megamenu-in.active {
  display: block; }

.yoo-nav.yoo-mobile-nav {
  max-height: calc(100vh - 60px);
  overflow: auto; }

.yoo-mobile-nav .yoo-nav-list .yoo-megamenu-list {
  display: block; }

.yoo-mobile-nav .yoo-megamenu-title {
  padding: 7px 15px; }

.yoo-mobile-nav .yoo-nav-list .yoo-megamenu-list a {
  padding-left: 10px;
  padding-right: 10px; }

.yoo-mobile-nav .yoo-megamenu-in {
  display: none; }

.yoo-mobile-nav .yoo-dropdown-btn {
  height: 20px;
  width: 20px;
  position: absolute;
  right: 15px;
  top: 9px;
  cursor: pointer;
  z-index: 1; }

.yoo-mobile-nav .yoo-dropdown-btn:before,
.yoo-mobile-nav .yoo-dropdown-btn:after {
  content: "";
  position: absolute;
  height: 2px;
  width: 8px;
  background-color: rgba(255, 255, 255, 0.38);
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-mobile-nav .yoo-dropdown-btn:before {
  -webkit-transform: translate(-50%, -50%) rotate(90deg);
  transform: translate(-50%, -50%) rotate(90deg); }

.yoo-mobile-nav .yoo-dropdown-btn.yoo-active:before {
  -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg); }

/*End Horizontal Mobile Menu*/
/*=== Mobile Menu Button ===*/
.yoo-nav-toggle {
  position: relative;
  cursor: pointer;
  font-size: 23px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: rgba(255, 255, 255, 0.38);
  margin-left: -9px;
  display: none;
  padding: 0 10px; }
  .yoo-nav-toggle.yoo-active {
    color: rgba(255, 255, 255, 0.6); }

/*Ene Mobile Menu Button*/
/*=== Horizontal Menu Animation Efect ===*/
.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-has-children > ul {
  margin-top: 15px; }

.yoo-fade-up .yoo-desktop-nav .yoo-has-children:hover > ul {
  margin-top: 0px; }

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 ul {
  margin-left: 0;
  padding: 10px 0;
  list-style: none;
  position: absolute;
  background-color: #fff;
  width: 100%;
  left: 100%;
  top: 0;
  margin-top: 15px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #e5e5e5; }

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 .yoo-has-children:hover > ul {
  opacity: 1;
  visibility: visible;
  margin-top: 0; }

.yoo-fade-up .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  margin-top: 15px; }

.yoo-fade-up .yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  margin-top: -10px; }

.yoo-fade-down .yoo-desktop-nav .yoo-nav-list .yoo-has-children > ul {
  margin-top: -20px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-fade-down .yoo-desktop-nav .yoo-has-children:hover > ul {
  margin-top: -10px; }

.yoo-fade-down .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  margin-top: -20px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-fade-down .yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  margin-top: -10px; }

/*Ene Horizontal Menu Animation Effect*/
/*=== Menu Responsive Style ===*/
@media screen and (min-width: 991px) {
  .container {
    max-width: 1200px;
    width: 100%; } }
@media screen and (max-width: 991px) {
  .yoo-nav-toggle,
  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
    display: block; }

  .yoo-nav.yoo-mobile-nav,
  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style3 .yoo-megamenu-in {
    display: none; }

  .yoo-header .container {
    max-width: 100%; }

  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
    width: 100%;
    padding: 0px 15px; }

  .yoo-megamenu.yoo-style2 .yoo-megamenu-in {
    padding: 0; }

  .yoo-dropdown.yoo-style2 li,
  .yoo-dropdown.yoo-style3 li,
  .yoo-dropdown.yoo-style4 li,
  .yoo-dropdown.yoo-style5 li,
  .yoo-dropdown.yoo-style6 li,
  .yoo-dropdown.yoo-style7 li {
    padding-left: 0;
    padding-right: 0px; }

  .yoo-dropdown.yoo-style2 a,
  .yoo-dropdown.yoo-style3 a,
  .yoo-dropdown.yoo-style4 a {
    padding-left: 0;
    padding-right: 0; }

  .yoo-dropdown.yoo-style2 .yoo-dropdown-cta,
  .yoo-dropdown.yoo-style3 .yoo-dropdown-cta,
  .yoo-dropdown.yoo-style4 .yoo-dropdown-cta {
    margin-top: 0;
    padding-top: 0;
    border: none; }

  .yoo-dropdown.yoo-style2 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style3 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style4 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style5 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style6 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style7 .yoo-subdropdown-title {
    padding-left: 0 !important; }

  .yoo-dropdown.yoo-style5 a,
  .yoo-dropdown.yoo-style6 a,
  .yoo-dropdown.yoo-style7 a {
    padding-left: 0;
    padding-right: 0; }

  .yoo-mobile-nav {
    border-bottom: 1px solid #e5e5e5; }

  .yoo-megamenu.yoo-style2 .container {
    padding-left: 15px; }

  .yoo-megamenu.yoo-style6 .yoo-megamenu-in {
    padding-top: 10px;
    padding-bottom: 10px; }

  .yoo-header.yoo-style2 .yoo-main-header-in {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between; }

  .yoo-nav .yoo-has-children .yoo-dropdown {
    position: initial;
    opacity: initial;
    visibility: visible;
    width: calc(100% - 15px);
    display: none;
    -webkit-transition: initial;
    transition: initial; }

  .yoo-nav-wrap .yoo-nav-toggle {
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; } }
/*End Menu Responsive*/
/*=== Vertical Menu Style ===*/
.yoo-vertical-nav-wrap {
  height: 100%;
  width: 240px;
  margin-right: 30px; }

.yoo-vertical-nav-btn {
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #007aff;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px 4px 0 0;
  width: 100%;
  font-size: 14px;
  position: relative;
  z-index: 1; }

.yoo-vertical-nav-btn i {
  margin-right: 8px; }

.yoo-vertical-nav.yoo-style1 {
  width: 240px; }

.yoo-vertical-nav-list {
  padding: 0;
  margin: 0;
  list-style: none;
  background-color: #fff;
  position: relative;
  width: 100%;
  -webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1); }

.yoo-desktop-nav .yoo-vertical-nav-btn.yoo-vertical-nav-perform {
  pointer-events: none; }

.yoo-vertical-nav-list li a {
  font-size: 15px;
  color: rgba(0, 0, 0, 0.7);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 8px 20px;
  width: 100%;
  line-height: 1.65em; }

.yoo-vertical-nav-list .yoo-vertical-cta > a {
  font-size: 12px;
  font-weight: 700;
  padding: 11px 20px;
  line-height: 1.5em; }

.yoo-vertical-nav-list > li {
  border-bottom: 1px solid #e5e5e5; }

.yoo-vertical-nav-list > li:last-child {
  border-bottom: none; }

.yoo-vertical-nav-list li a:hover {
  background-color: rgba(0, 0, 0, 0.02); }

.yoo-vertical-nav-list > li > a > i {
  margin-right: 10px; }

.yoo-desktop-nav .yoo-has-children.yoo-vertical-megamenu {
  position: initial; }

.yoo-vertical-arrow {
  display: inline-block;
  background-image: url(../img/arrow.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 8px;
  width: 8px;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
  margin-left: 7px;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }

.yoo-desktop-nav .yoo-vertical-megamenu-in {
  position: absolute;
  left: 100%;
  margin-left: 0;
  width: calc(1170px - 240px);
  min-height: 100%;
  background: #fff;
  top: 0;
  -webkit-box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  border-left: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5; }

.yoo-has-children:hover .yoo-vertical-megamenu-in {
  opacity: 1;
  visibility: visible; }

.yoo-vertical-megamenu-in .container {
  padding: 0; }

.yoo-vertical-megamenu-in .row {
  margin: 0; }

.yoo-vertical-megamenu-in .row > div {
  padding: 0;
  border-top: 1px solid #e5e5e5;
  margin-top: -1px; }

.yoo-desktop-nav.yoo-vertical-nav-wrap .yoo-megamenu-col {
  border-right: none; }

/*Vertical Mobile menu*/
.yoo-vertical-nav-wrap.yoo-mobile-nav {
  position: relative;
  top: 0;
  padding: 0; }

.yoo-mobile-nav .yoo-has-children .yoo-vertical-arrow {
  display: none; }

.yoo-mobile-nav .yoo-vertical-nav-list {
  padding-left: 0;
  display: block !important;
  height: initial !important; }

.yoo-mobile-nav .yoo-vertical-nav-list li a {
  padding-left: 15px;
  padding-right: 15px; }

.yoo-mobile-nav .yoo-vertical-nav.yoo-style1 {
  max-height: calc(100vh - 60px);
  overflow: auto;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  width: 240px;
  margin-left: -300px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-mobile-nav .yoo-mobile-active + .yoo-vertical-nav.yoo-style1 {
  margin-left: 0; }

.yoo-mobile-nav .yoo-vertical-nav-list {
  width: 100%; }

.yoo-mobile-nav .yoo-vertical-megamenu-in {
  display: none; }

.yoo-mobile-nav .yoo-vertical-megamenu-in .row > div {
  margin-top: 0; }

.yoo-mobile-nav .yoo-megamenu-col {
  padding: 10px 0; }

/*End Vertical Menu Style*/
/*=== Vertical Menu Animation Efect ===*/
.yoo-fade-left.yoo-desktop-nav .yoo-vertical-megamenu-in {
  margin-left: -30px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-fade-left.yoo-desktop-nav .yoo-has-children:hover .yoo-vertical-megamenu-in {
  margin-left: 0px; }

.yoo-fade-left.yoo-desktop-nav .yoo-has-children .yoo-vertical-megamenu-in {
  display: block !important; }

/*End Vertical Menu Animation Efect*/
.yoo-main-header-left {
  width: 300px;
  /* border-right: 2px solid rgba(255, 255, 255, 0.1); */
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 15px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-main-header-right {
  padding: 0 30px;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between; }

.yoo-profile-nav.yoo-style1 {
  padding-left: 20px;
  border-left: 1px solid rgba(255, 255, 255, 0.1); }
  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    cursor: pointer;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-profile-nav.yoo-style1 .yoo-profile-nav-btn:hover {
      opacity: 0.8; }
  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-text {
    text-align: right;
    margin-right: 8px; }
    .yoo-profile-nav.yoo-style1 .yoo-profile-nav-text span {
      color: rgba(255, 255, 255, 0.38);
      font-size: 12px;
      display: block;
      line-height: 1.3em; }
    .yoo-profile-nav.yoo-style1 .yoo-profile-nav-text h4 {
      margin-bottom: 0;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 500; }
  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-img {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    border-radius: 50%;
    overflow: hidden;
    height: 32px;
    width: 32px; }

.yoo-ex-nav-btn {
  display: block;
  color: rgba(255, 255, 255, 0.54);
  font-size: 24px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 8px 0;
  border-radius: 4px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-toggle-body:hover .yoo-ex-nav-btn {
  color: #fff; }

.yoo-toggle-body .yoo-ex-nav-btn.active {
  border-radius: 4px 4px 0 0; }

.yoo-ex-nav-label {
  position: absolute;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.9);
  background-color: #ff3b30;
  line-height: 1.4em;
  padding: 0 4px;
  border-radius: 50%;
  top: 5px;
  right: 0px;
  -webkit-transform: translateX(40%);
          transform: translateX(40%);
  z-index: 2;
  min-width: 15px; }

.yoo-ex-nav.yoo-style1 > li:not(:first-child) {
  margin-left: 20px; }

.yoo-notice-area.yoo-style1 .yoo-ex-nav-label {
  -webkit-transform: translateX(20%);
          transform: translateX(20%); }

.yoo-toggle-body {
  position: relative; }
  .yoo-toggle-body .yoo-dropdown.yoo-style1 {
    left: initial;
    right: 0;
    margin-top: 10px; }
    .yoo-toggle-body .yoo-dropdown.yoo-style1.active {
      visibility: visible;
      opacity: 1;
      margin-top: 0; }
  .yoo-toggle-body .yoo-dropdown.yoo-notify-dropdown {
    left: initial;
    right: 0;
    margin-top: 10px;
    width: 400px;
    padding: 0; }
    .yoo-toggle-body .yoo-dropdown.yoo-notify-dropdown.active {
      visibility: visible;
      opacity: 1;
      margin-top: 0; }
  .yoo-toggle-body.yoo-start-left .yoo-dropdown.yoo-notify-dropdown {
    right: inherit;
    left: 0; }

.yoo-notify-dropdown .yoo-btn.yoo-style2 {
  padding: 8px 15px 7px;
  background-color: rgba(0, 0, 0, 0.05); }
  .yoo-notify-dropdown .yoo-btn.yoo-style2:hover {
    background-color: rgba(0, 0, 0, 0.05); }

.yoo-msg {
  line-height: 1.45em;
  margin-top: 2px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.4); }

.yoo-nofify-list.yoo-style1 {
  background: #fff; }
  .yoo-nofify-list.yoo-style1 li {
    border-bottom: 1px solid #e5e5e5; }
    .yoo-nofify-list.yoo-style1 li > a,
    .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      padding: 13px 15px;
      position: relative;
      -webkit-transition: all 0.3s ease;
      transition: all 0.3s ease; }
      .yoo-nofify-list.yoo-style1 li > a.active,
      .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in.active {
        background-color: rgba(0, 0, 0, 0.02); }
      .yoo-nofify-list.yoo-style1 li > a:hover,
      .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in:hover {
        background-color: rgba(0, 0, 0, 0.05);
        color: rgba(0, 0, 0, 0.7); }
    .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in .yoo-nofify-text-head {
      color: rgba(0, 0, 0, 0.95); }
      .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in .yoo-nofify-text-head a {
        color: rgba(0, 0, 0, 0.95); }
        .yoo-nofify-list.yoo-style1 li .yoo-nofify-list-in .yoo-nofify-text-head a:hover {
          color: rgba(0, 0, 0, 0.9); }
  .yoo-nofify-list.yoo-style1 .yoo-nofify-thumb {
    height: 48px;
    width: 48px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 10px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-thumb img {
      height: 100%;
      width: 100%;
      -o-object-fit: cover;
         object-fit: cover; }
  .yoo-nofify-list.yoo-style1 .yoo-nofify-text {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    position: relative; }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-text .yoo-nofify-text-head {
      font-size: 14px;
      margin-bottom: 3px;
      font-weight: 400;
      line-height: 1.2em; }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-text .yoo-notify-time {
      color: rgba(0, 0, 0, 0.4);
      line-height: 1em;
      font-size: 13px; }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-text strong {
      font-weight: 600; }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-text span {
      color: rgba(0, 0, 0, 0.7); }
    .yoo-nofify-list.yoo-style1 .yoo-nofify-text .yoo-msg + .yoo-notify-time {
      font-size: 12px; }
  .yoo-nofify-list.yoo-style1 .yoo-online-status {
    margin-left: 4px;
    margin-bottom: 2px; }

.yoo-message-area .yoo-nofify-list.yoo-style1 .yoo-nofify-text .yoo-notify-time {
  position: absolute;
  top: 2px;
  right: 0; }

.yoo-nofify-list.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 10px 5px 0; }
  .yoo-nofify-list.yoo-style2 li {
    width: 33.333333%;
    margin-bottom: 10px;
    padding: 0px 5px; }
  .yoo-nofify-list.yoo-style2 a {
    display: block;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 20px 0; }
    .yoo-nofify-list.yoo-style2 a:hover {
      border-color: #e5e5e5; }
  .yoo-nofify-list.yoo-style2 .yoo-nofify-icon {
    margin: auto;
    margin-top: 5px; }
  .yoo-nofify-list.yoo-style2 .yoo-nofify-text h3 {
    text-align: center;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.7);
    font-weight: 400;
    margin-bottom: 3px;
    margin-top: 9px; }
  .yoo-nofify-list.yoo-style2 .yoo-nav-icon {
    text-align: center;
    max-width: 60px;
    margin: auto;
    border-radius: 10px;
    overflow: hidden; }

.yoo-nofify-title {
  font-size: 14px;
  margin-bottom: 0;
  padding: 12px 15px;
  border-bottom: 1px solid #e5e5e5;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font-weight: 500; }
  .yoo-nofify-title .yoo-nofify-title-right {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.4); }
  .yoo-nofify-title a:hover {
    color: rgba(0, 0, 0, 0.7); }

.yoo-nofify-icon {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  height: 48px;
  width: 48px;
  border-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 10px;
  font-size: 21px;
  overflow: hidden; }
  .yoo-nofify-icon img {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover; }

/* Start Search Area */
.yoo-dropdown.active .yoo-search-input {
  border-radius: 4px; }

.yoo-search-area.yoo-toggle-body .yoo-dropdown {
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  width: 120px;
  padding: 0;
  top: 5px;
  left: initial;
  right: 0; }

.yoo-search-area.yoo-toggle-body.yoo-start-left .yoo-dropdown {
  right: inherit;
  left: 0; }

.yoo-search-area.yoo-toggle-body .yoo-dropdown.active {
  opacity: 1;
  visibility: visible;
  width: 250px; }

.yoo-search-area.yoo-toggle-body .yoo-toggle-btn.active {
  visibility: hidden; }

/* End Search Area */
.yoo-header.yoo-sticky-header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0; }

.yoo-header.yoo-style3 {
  padding: 30px 40px;
  padding-right: 70px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-header.yoo-style3 .yoo-main-header-in {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    position: relative; }
  .yoo-header.yoo-style3 .yoo-main-header-left {
    border: none; }
  .yoo-header.yoo-style3 .yoo-main-header-right {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    padding: 0; }
  .yoo-header.yoo-style3 .yoo-nav-list > li > a {
    color: rgba(0, 0, 0, 0.7);
    font-size: 14px;
    padding: 13px 10px; }
  .yoo-header.yoo-style3 .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
    left: initial;
    right: 0; }
  .yoo-header.yoo-style3 .yoo-has-children.yoo-megamenu {
    position: initial; }
  .yoo-header.yoo-style3 .yoo-desktop-nav .yoo-nav-list > li:hover > a {
    background-color: transparent;
    color: rgba(0, 0, 0, 0.95); }

.yoo-header.yoo-style3.yoo-sticky-header.yoo-sticky-active {
  padding-top: 10px;
  padding-bottom: 10px;
  background: #fff;
  border-bottom: 1px solid #eaeaea; }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: #007aff; }

.nav-item a,
.nav-pills .nav-link {
  color: rgba(0, 0, 0, 0.7);
  letter-spacing: 0.17px; }

.nav-item a.dropdown-item {
  color: rgba(0, 0, 0, 0.7); }

.nav-item a.dropdown-item.active {
  color: #007aff;
  background-color: rgba(0, 122, 255, 0.1); }

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.95);
  font-weight: 500; }

.navbar.flex-column .nav {
  width: 100%; }

.navbar.flex-column {
  padding: 10px; }

.navbar.flex-column .nav {
  width: 100%; }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff; }

.nav-item .nav-link:hover {
  color: #007aff; }

.nav-link.disabled {
  color: rgba(0, 0, 0, 0.4); }

.nav-item .nav-link.active {
  color: #007aff; }

.nav-tabs,
.nav-pills {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #f0f0f7;
  border-radius: 5px;
  padding: 3px;
  border: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex; }
  .nav-tabs .nav-item,
  .nav-pills .nav-item {
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    padding: 0;
    margin: 0; }
  .nav-tabs .nav-link,
  .nav-pills .nav-link {
    border: none;
    line-height: 1.6em;
    font-size: 13px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 7px 30px;
    border-radius: 5px;
    color: rgba(0, 0, 0, 0.95);
    position: relative;
    z-index: 1;
    font-weight: 400; }
    .nav-tabs .nav-link:hover,
    .nav-pills .nav-link:hover {
      opacity: 0.6;
      color: rgba(0, 0, 0, 0.95); }
    .nav-tabs .nav-link.active,
    .nav-pills .nav-link.active {
      color: rgba(0, 0, 0, 0.95);
      font-weight: 500; }
      .nav-tabs .nav-link.active:hover,
      .nav-pills .nav-link.active:hover {
        opacity: 1; }

.nav-pills .nav-link.active {
  background-color: #007aff;
  color: #fff; }

.nav-fill,
.nav.flex-column {
  width: 100%; }
  .nav-fill .nav-link,
  .nav.flex-column .nav-link {
    width: 100%; }

.nav.flex-column {
  padding: 3px; }

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  background-color: #fff;
  border: none; }

.yoo-card.yoo-style1 .nav-tabs .nav-item:not(:last-child) {
  margin-right: 1px; }

.nav-tabs .nav-item .nav-link.active {
  z-index: 2;
  -webkit-box-shadow: 0px 2px 11.52px 0.48px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 11.52px 0.48px rgba(0, 0, 0, 0.1); }

.nav-tabs .nav-item .nav-link:before,
.nav-tabs .nav-item .nav-link:after {
  content: '';
  position: absolute;
  height: 20px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%); }

.nav-tabs .nav-item .nav-link.active:before,
.nav-tabs .nav-item .nav-link.active:after {
  background-color: #f0f0f7; }

.nav-tabs .nav-item .nav-link:before {
  right: -1px; }

.nav-tabs .nav-item .nav-link:after {
  left: -1px; }

.nav-tabs .nav-item:last-child .nav-link:before {
  display: none; }

.nav-tabs .nav-item:first-child .nav-link:after {
  display: none; }

.yoo-card.yoo-style1 .nav-tabs .nav-item .nav-link:before,
.yoo-card.yoo-style1 .nav-tabs .nav-item .nav-link:after {
  height: 15px; }

/* Header Responsive */
@media screen and (max-width: 1199px) {
  .yoo-mobile-nav .yoo-megamenu-title {
    color: rgba(255, 255, 255, 0.38); } }
@media screen and (max-width: 991px) {
  .yoo-main-header-right {
    padding: 0 20px; }

  .yoo-desktop-nav .yoo-nav-list > li {
    margin-left: 18px; }

  .yoo-nav-list ul a,
  .yoo-profile-nav .yoo-dropdown a {
    color: rgba(255, 255, 255, 0.6); } }
@media screen and (max-width: 767px) {
  .yoo-main-header-left {
    width: initial;
    padding-left: 58px;
    border-right-width: 1px; }

  .yoo-logo-link img {
    margin-left:30px;
    height: 28px; }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-text {
    display: none; }

  .yoo-profile-nav.yoo-style1 {
    margin-left: 0px;
    padding-left: 15px; }

  .yoo-main-header .yoo-toggle-body {
    position: initial; }

  .yoo-main-header .yoo-toggle-body .yoo-dropdown.yoo-notify-dropdown {
    right: initial;
    left: 50%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    width: 100%; }

  .yoo-mobile-nav .yoo-megamenu-col {
    padding: 0; }

  .yoo-main-header .yoo-toggle-body.yoo-search-area {
    position: relative; } }
@media screen and (max-width: 575px) {
  .yoo-ex-nav.yoo-style1 > li:not(:first-child) {
    margin-left: 15px; } }
@media screen and (max-width: 485px) {
  .yoo-logo-link img {
    margin-left:30px;
    height: 22px; }

  .yoo-ex-nav-btn {
    font-size: 22px; }

  .yoo-ex-nav.yoo-style1 > li:not(:first-child) {
    margin-left: 12px; }

  .yoo-main-header .yoo-toggle-body {
    border: none;
    padding-left: 0; }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-img {
    height: 28px;
    width: 28px; } }
@media screen and (max-width: 410px) {
  .yoo-main-header-left {
    padding-left: 53px; }

  .yoo-main-header-right {
    padding-left: 15px; }

  .yoo-logo-link img {
    margin-left:30px;
    height: 20px; }

  .yoo-nav-toggle,
  .yoo-ex-nav-btn {
    font-size: 20px; }

  .yoo-ex-nav.yoo-style1 > li:not(:first-child) {
    margin-left: 8px; }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-img {
    height: 22px;
    width: 22px; }

  .yoo-search-area.yoo-toggle-body .yoo-dropdown.active {
    width: 180px; } }
@media screen and (max-width: 360px) {
  .yoo-sidebarheader-toggle {
    left: 10px; }

  .yoo-main-header-right {
    padding-right: 10px;
    padding-left: 0; }

  .yoo-main-header-left {
    padding-left: 40px;
    border-right: none; } }
/*========== End Header  ==========*/
.yoo-sidebarheader {
  width: 300px;
  background-color: #fff;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  left: 0;
  top: 0;
  padding-top: 60px;
  z-index: 50;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-sidebarheader .yoo-sidebarheader-in,
  .yoo-sidebarheader .yoo-blur-wrap-in {
    height: 100vh;
    height: 100%; }
  .yoo-sidebarheader.yoo-white-bg .yoo-sidebarheader-in {
    background-color: #fff; }
  .yoo-sidebarheader .yoo-search.yoo-style1 {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
  .yoo-sidebarheader.yoo-blur-wrap {
    -webkit-box-shadow: -5px 0 10px rgba(0, 0, 0, 0.1);
            box-shadow: -5px 0 10px rgba(0, 0, 0, 0.1);
    background: transparent; }
    .yoo-sidebarheader.yoo-blur-wrap .yoo-sidebar-search {
      border-color: rgba(0, 0, 0, 0.1); }
    .yoo-sidebarheader.yoo-blur-wrap .yoo-sidebar-nav-list li.active > a {
      background-color: rgba(0, 0, 0, 0.05);
      color: rgba(0, 0, 0, 0.95); }

.yoo-sidebar-link-text,
.yoo-sidebar-nav-list .yoo-nav-label,
.yoo-dropdown-arrow,
.yoo-sidebar-link-icon {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-sidebar-nav {
  padding: 17px 0px 40px;
  overflow: hidden;
  width: 300px; }

.yoo-sidebar-nav-list {
  color: rgba(0, 0, 0, 0.95);
  margin-bottom: 10px; }
  .yoo-sidebar-nav-list:last-child {
    margin-bottom: 0; }
  .yoo-sidebar-nav-list ul {
    margin: 0;
    padding: 0;
    list-style: none; }
  .yoo-sidebar-nav-list a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    color: rgba(0, 0, 0, 0.95);
    position: relative;
    cursor: pointer;
    position: relative;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-weight: 400;
    letter-spacing: 0;
    font-size: 16px; }
    .yoo-sidebar-nav-list a:hover {
      background-color: rgba(0, 0, 0, 0.05); }
    .yoo-sidebar-nav-list a .yoo-sidebar-link-title {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .yoo-sidebar-nav-list a .yoo-sidebar-link-text {
      line-height: 1.25em; }
  .yoo-sidebar-nav-list .yoo-sidebar-link-icon {
    font-size: 20px;
    margin-right: 15px;
    height: 30px;
    width: 30px;
    border-radius: 5px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    color: #fff; }
    .yoo-sidebar-nav-list .yoo-sidebar-link-icon.yoo-style1 {
      color: #919198;
      font-size: 24px;
      width: initial;
      margin-right: 12px; }
  .yoo-sidebar-nav-list > li a {
    padding: 6px 20px; }
  .yoo-sidebar-nav-list .yoo-nav-label {
    font-size: 10px;
    line-height: 1.5em;
    padding: 0 4px;
    border-radius: 2px;
    color: rgba(255, 255, 255, 0.9);
    background-color: #ff3b30; }
  .yoo-sidebar-nav-list li {
    position: relative; }
    .yoo-sidebar-nav-list li.active > a {
      color: #007aff;
      background-color: rgba(0, 122, 255, 0.1); }
      .yoo-sidebar-nav-list li.active > a i {
        color: inherit; }
      .yoo-sidebar-nav-list li.active > a:hover {
        color: #007aff; }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children > a {
      padding-right: 35px; }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-dropdown-arrow {
      position: absolute;
      right: 20px;
      top: 12px;
      z-index: 2;
      cursor: pointer;
      border-left: none;
      border-bottom: none;
      pointer-events: none;
      color: rgba(0, 0, 0, 0.3);
      font-size: 18px; }
      .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-dropdown-arrow .hydrated {
        -webkit-transition: all 0.4s ease;
        transition: all 0.4s ease; }
      .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-dropdown-arrow.active .hydrated {
        -webkit-transform: rotate(90deg);
                transform: rotate(90deg); }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-sidebar-nav-dropdown > li a {
      padding-left: 56px;
      padding-top: 8px;
      padding-bottom: 12px;
      padding-top: 12px;
      font-size: 15px; }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-sidebar-nav-dropdown .yoo-sidebar-nav-dropdown > li a {
      padding-left: 80px; }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-sidebar-nav-dropdown .yoo-sidebar-nav-dropdown .yoo-sidebar-nav-dropdown > li a {
      padding-left: 95px; }
    .yoo-sidebar-nav-list li.yoo-sidebar-has-children .yoo-sidebar-nav-dropdown .yoo-sidebar-link-icon {
      font-size: 16px; }
  .yoo-sidebar-nav-list .yoo-sidebar-nav-dropdown {
    display: none; }

.yoo-sidebar-nav-title {
  text-transform: uppercase;
  padding: 9px 20px;
  color: rgba(0, 0, 0, 0.3);
  font-weight: 500;
  font-size: 12px;
  min-height: 29px;
  position: relative;
  width: 300px; }

.yoo-sidebar-nav-title-text {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-sidebar-nav-title-dotline {
  position: absolute;
  height: 100%;
  width: 66px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  top: 0;
  left: 0;
  font-size: 21px;
  color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-sidebar-active .yoo-sidebar-nav-title-dotline {
  opacity: 1; }

.yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-nav-title-dotline {
  opacity: 0; }

.yoo-sidebar-nav-dot {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  top: 0;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 19px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.yoo-sidebarheader-toggle {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-item-align: center;
      align-self: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 18px;
  height: 12px;
  cursor: pointer;
  z-index: 100;
  position: fixed;
  top: 24px;
  left: 252px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-sidebarheader-toggle div {
    -ms-flex-item-align: end;
    align-self: flex-end;
    height: 2px;
    width: 100%;
    background: rgba(255, 255, 255, 0.38); }
    .yoo-sidebarheader-toggle div.yoo-button-bar2 {
      width: 65.5%;
      -webkit-transition: all 200ms ease-in-out;
      transition: all 200ms ease-in-out; }
    .yoo-sidebarheader-toggle div.yoo-button-bar3 {
      width: 35%;
      -webkit-transition: all 400ms ease-in-out;
      transition: all 400ms ease-in-out; }
  .yoo-sidebarheader-toggle:hover div {
    width: 100%; }
  .yoo-sidebarheader-toggle:hover .yoo-button-bar1 {
    -webkit-animation: burger-hover 1s infinite ease-in-out alternate;
            animation: burger-hover 1s infinite ease-in-out alternate; }
  .yoo-sidebarheader-toggle:hover .yoo-button-bar2 {
    -webkit-animation: burger-hover 1s infinite ease-in-out alternate forwards 200ms;
            animation: burger-hover 1s infinite ease-in-out alternate forwards 200ms; }
  .yoo-sidebarheader-toggle:hover .yoo-button-bar3 {
    -webkit-animation: burger-hover 1s infinite ease-in-out alternate forwards 400ms;
            animation: burger-hover 1s infinite ease-in-out alternate forwards 400ms; }

@-webkit-keyframes burger-hover {
  0% {
    width: 100%; }
  50% {
    width: 50%; }
  100% {
    width: 100%; } }

@keyframes burger-hover {
  0% {
    width: 100%; }
  50% {
    width: 50%; }
  100% {
    width: 100%; } }
/* Start Sidebar heading Toggle Function */
.yoo-sidebar-active {
  /* Hover */ }
  .yoo-sidebar-active .yoo-content.yoo-style1 {
    padding-left: 66px; }
  .yoo-sidebar-active .yoo-main-header-left {
    margin-left: -234px; }
  
  .yoo-sidebar-active .yoo-sidebarheader {
    width: 66px; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-link-icon {
      margin-right: 0; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-dropdown,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-nav-label,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-link-text,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-title-text,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-site-branding {
      opacity: 0;
      visibility: hidden; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-dot {
      opacity: 1;
      visibility: visible; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-dropdown li {
      display: none; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebarheader {
    width: 300px; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-nav-dropdown,
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-nav-label,
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-link-text,
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-nav-title-text,
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-site-branding {
    opacity: 1;
    visibility: visible; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-nav-dot {
    opacity: 0;
    visibility: hidden; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-link-icon {
    margin-right: 15px; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-nav-dropdown li {
    display: block; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-search.yoo-style1 {
    width: 100%; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-sidebar-search .yoo-voice-btn {
    display: inline-block; }
  .yoo-sidebar-active.yoo-sidebar-hover-active .yoo-search.yoo-style1 .yoo-search-submit {
    width: 34px; }
  .yoo-sidebar-active .yoo-search.yoo-style1 {
    width: 36px;
    overflow: hidden;
    border-radius: 10px; }
  .yoo-sidebar-active .yoo-sidebar-search .yoo-voice-btn {
    display: none; }
  .yoo-sidebar-active .yoo-search.yoo-style1 .yoo-search-submit {
    width: 36px; }
  .yoo-sidebar-active .yoo-with-boxed-icon .yoo-sidebar-nav-list .yoo-sidebar-link-icon {
    margin-left: -2px; }
  .yoo-sidebar-active .yoo-sidebar-nav-title-dotline {
    left: -1px; }

/* End Sidebar heading Toggle Function */
.yoo-sidebar-nav-list li {
  padding-bottom: 5px; }
  .yoo-sidebar-nav-list li:last-child {
    padding-bottom: 0; }

.yoo-sidebar-nav-list .yoo-sidebar-nav-dropdown {
  padding-top: 5px; }

@media screen and (max-width: 1199px) {
  .yoo-sidebarheader {
    left: -300px; }

  .yoo-content.yoo-style1 {
    padding-left: 0; }

  .yoo-sidebar-active .yoo-content.yoo-style1 {
    padding-left: 0px; }
  .yoo-sidebar-active .yoo-sidebarheader {
    width: 300px;
    left: 0;
    background-color: #fff; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-link-icon {
      margin-right: 15px; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-dropdown,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-nav-label,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-link-text,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-title-text,
    .yoo-sidebar-active .yoo-sidebarheader .yoo-site-branding {
      opacity: 1;
      visibility: visible; }
    .yoo-sidebar-active .yoo-sidebarheader .yoo-sidebar-nav-dot {
      opacity: 0;
      visibility: hidden; }
  .yoo-sidebar-active .yoo-main-header-left {
    margin-left: 0px; } }
.yoo-sidebar-search {
  display: none;
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
  .yoo-sidebar-search .yoo-search.yoo-style1 .yoo-search-input {
    background-color: rgba(0, 0, 0, 0.04);
    border-color: transparent;
    border-radius: 10px; }
  .yoo-sidebar-search .yoo-voice-btn {
    position: absolute;
    background-color: transparent;
    border: none;
    font-size: 24px;
    padding: 0;
    top: 5px;
    right: 15px;
    color: rgba(0, 0, 0, 0.3);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-sidebar-search .yoo-voice-btn:hover {
      color: rgba(0, 0, 0, 0.4); }

.yoo-sidebarheader.yoo-sidebarheader1 {
  padding-top: 0;
  width: 360px; }
  .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav {
    width: 100%; }
  .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list {
    margin-bottom: 0;
    padding-bottom: 30px;
    border-bottom: 1px solid #e5e5e5; }
    .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list li > a {
      padding: 6px 30px;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list .yoo-nav-label {
      margin-top: 0;
      background-color: #34c759; }
    .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list a:hover {
      background-color: rgba(0, 0, 0, 0.05); }
    .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list li.active > a, .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list li.active > a:hover {
      background-color: rgba(0, 122, 255, 0.1); }
    .yoo-sidebarheader.yoo-sidebarheader1 .yoo-sidebar-nav-list li.active > a:before {
      display: none; }

.yoo-sidebar-nav-list .active .yoo-sidebar-link-icon.yoo-style1 {
  color: #007aff; }

.yoo-sidebarheader.yoo-sidebarheader1.yoo-type1 .yoo-sidebar-nav-list li > a {
  padding: 10px 30px;
  font-weight: 500; }

.yoo-sidebarheader-toggle.yoo-style1 {
  display: none; }

.yoo-blur-wrap .yoo-sidebar-search,
.yoo-blur-wrap .yoo-sidebar-nav {
  position: relative;
  z-index: 4; }
.yoo-blur-wrap [data-scrollbar] .scrollbar-thumb:before,
.yoo-blur-wrap [data-scrollbar] .scrollbar-thumb:after {
  background: rgba(0, 0, 0, 0.025); }

.yoo-sidebarheader.yoo-sidebarheader1.yoo-type1.yoo-messenger-sidebar {
  border-right: 1px solid #e5e5e5; }

@media screen and (max-width: 1199px) {
  .yoo-sidebarheader.yoo-sidebarheader1 {
    left: -100%;
    width: 100%; }

  .yoo-sidebarheader-toggle.yoo-style1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    height: 80px;
    width: 80px;
    top: 0;
    left: 0;
    border-right: 1px solid #e5e5e5;
    padding: 33px 30px; }

  .yoo-sidebar-active .yoo-sidebarheader.yoo-sidebarheader1 {
    left: 0px; }

  .yoo-sidebar-active .yoo-sidebarheader-toggle.yoo-style1 {
    left: 100%;
    margin-left: -80px; }

  .yoo-sidebarheader .yoo-search.yoo-style2 {
    position: inherit; } }
@media screen and (max-width: 767px) {
  .yoo-sidebarheader-toggle {
    left: 20px; } }
@media screen and (max-width: 575px) {
  .yoo-sidebar-active .yoo-main-header-left {
    margin-left: -208px;
    left: 258px;
    position: relative;
    z-index: 10;
    background-color: #212121; } }
@media screen and (max-width: 360px) {
  .yoo-sidebarheader-toggle {
    left: 10px; } }
/*========== Start Button  ==========*/
.yoo-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer; }

.yoo-btn.yoo-style1 {
  border: 1px solid #e5e5e5;
  padding: 0.5em 1.6em;
  font-size: 14px;
  font-weight: 500;
  background-color: #fff;
  border-radius: 4px; }
  .yoo-btn.yoo-style1 i,
  .yoo-btn.yoo-style1 .hydrated {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.4);
    margin-right: 10px; }
  .yoo-btn.yoo-style1.yoo-small {
    font-size: 13px;
    padding: 0.155em 1.2em; }
  .yoo-btn.yoo-style1.yoo-medium {
    font-size: 14px;
    padding: 0.25em 1.58em;
    border-radius: 4px; }
  .yoo-btn.yoo-style1:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: rgba(0, 0, 0, 0.7); }

.yoo-btn.yoo-style2 {
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  padding: 10px 15px;
  width: 100%; }
  .yoo-btn.yoo-style2 i,
  .yoo-btn.yoo-style2 .hydrated {
    margin-left: 0px;
    font-size: 13px; }
  .yoo-btn.yoo-style2:hover {
    color: rgba(0, 0, 0, 0.95);
    background-color: rgba(0, 0, 0, 0.05); }
  .yoo-btn.yoo-style2 .yoo-plus {
    font-size: 16px;
    font-weight: 500;
    margin-left: 0px;
    margin-right: 3px; }
  .yoo-btn.yoo-style2.yoo-type1 {
    font-weight: 500;
    padding: 12px 30px;
    width: 100%;
    text-transform: capitalize;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 13px; }
    .yoo-btn.yoo-style2.yoo-type1 i,
    .yoo-btn.yoo-style2.yoo-type1 .hydrated {
      margin-top: -1px; }
  .yoo-btn.yoo-style2.yoo-type2 {
    -webkit-box-shadow: 0.438px 0.899px 1px 0px rgba(0, 0, 0, 0.1);
            box-shadow: 0.438px 0.899px 1px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 7px 15px;
    font-size: 14px;
    text-transform: capitalize;
    font-weight: 500;
    background-color: #fff; }
    .yoo-btn.yoo-style2.yoo-type2:hover {
      background-color: rgba(0, 0, 0, 0.05); }

.yoo-btn.yoo-style3 {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  border-radius: 4px;
  padding: 0.58em 1.6em;
  background: #007aff;
  border: none;
  min-width: 130px; }
  .yoo-btn.yoo-style3:hover {
    opacity: 0.85; }

.yoo-btn.yoo-style4 {
  width: 24px;
  height: 24px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: #e5e5e5;
  border-radius: 4px;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }

.yoo-btn.yoo-style5 {
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  background-color: #007aff;
  border-radius: 4px;
  padding: 0.5em 1.6em; }
  .yoo-btn.yoo-style5 i,
  .yoo-btn.yoo-style5 .hydrated {
    font-style: initial;
    font-size: 24px;
    margin-right: 5px; }
  .yoo-btn.yoo-style5:hover {
    opacity: 0.85; }

.yoo-button-group.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: -5px; }
  .yoo-button-group.yoo-style2 .yoo-btn {
    margin-bottom: 5px;
    margin-right: 5px; }

.yoo-button-group.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-button-group.yoo-style1 .yoo-btn:not(:last-child) {
    margin-right: 15px; }
  .yoo-button-group.yoo-style1 .yoo-btn {
    margin-top: 10px; }

.yoo-button-group.yoo-style3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-button-group.yoo-style3 .yoo-btn:not(:last-child) {
    margin-right: 5px; }

.kit-btns .btn:not(:last-child) {
  margin-right: 6px; }

.btn-primary,
.btn-blue-gray,
.btn-secondary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-link,
.btn-light,
.btn-dark,
.btn-disabled {
  font-size: 16px;
  padding: 0.53em 1.43em;
  border: none;
  color: #fff;
  font-weight: 600;
  border-radius: 7px;
  line-height: 1.6em; }
  .btn-primary:hover, .btn-primary:focus, .btn-primary:active,
  .btn-blue-gray:hover,
  .btn-blue-gray:focus,
  .btn-blue-gray:active,
  .btn-secondary:hover,
  .btn-secondary:focus,
  .btn-secondary:active,
  .btn-success:hover,
  .btn-success:focus,
  .btn-success:active,
  .btn-danger:hover,
  .btn-danger:focus,
  .btn-danger:active,
  .btn-warning:hover,
  .btn-warning:focus,
  .btn-warning:active,
  .btn-info:hover,
  .btn-info:focus,
  .btn-info:active,
  .btn-link:hover,
  .btn-link:focus,
  .btn-link:active,
  .btn-light:hover,
  .btn-light:focus,
  .btn-light:active,
  .btn-dark:hover,
  .btn-dark:focus,
  .btn-dark:active,
  .btn-disabled:hover,
  .btn-disabled:focus,
  .btn-disabled:active {
    color: #fff; }

.btn-disabled,
.btn-primary.disabled,
.btn-primary:disabled,
.btn-secondary.disabled,
.btn-secondary:disabled {
  background-color: #f0f0f0;
  color: rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }
  .btn-disabled:hover,
  .btn-primary.disabled:hover,
  .btn-primary:disabled:hover,
  .btn-secondary.disabled:hover,
  .btn-secondary:disabled:hover {
    color: rgba(0, 0, 0, 0.4); }

.btn-primary {
  background-color: #007aff; }
  .btn-primary:hover {
    background-color: rgba(0, 122, 255, 0.8); }
  .btn-primary:focus, .btn-primary:active, .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(0, 122, 255, 0.72);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-primary.dropdown-toggle {
  background-color: rgba(0, 122, 255, 0.72);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-blue-gray {
  background-color: #8e8e93; }
  .btn-blue-gray:hover {
    background-color: rgba(142, 142, 147, 0.86); }
  .btn-blue-gray:focus, .btn-blue-gray:active, .btn-blue-gray:not(:disabled):not(.disabled).active, .btn-blue-gray:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(142, 142, 147, 0.8);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-blue-gray.dropdown-toggle {
  background-color: rgba(142, 142, 147, 0.8);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-secondary {
  background-color: #8e8e93; }
  .btn-secondary:hover {
    background-color: rgba(142, 142, 147, 0.8); }
  .btn-secondary:focus, .btn-secondary:active, .btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(142, 142, 147, 0.65);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-secondary.dropdown-toggle {
  background-color: rgba(142, 142, 147, 0.65);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-success {
  background-color: #34c759; }
  .btn-success:hover {
    background-color: rgba(52, 199, 89, 0.8); }
  .btn-success:focus, .btn-success:active, .btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(52, 199, 89, 0.65);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-success.dropdown-toggle {
  background-color: rgba(52, 199, 89, 0.65);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-danger {
  background-color: #ff3b30; }
  .btn-danger:hover {
    background-color: rgba(255, 59, 48, 0.8); }
  .btn-danger:focus, .btn-danger:active, .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(255, 59, 48, 0.65);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-danger.dropdown-toggle {
  background-color: rgba(255, 59, 48, 0.65);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-warning {
  background-color: #ff9500; }
  .btn-warning:hover {
    background-color: rgba(255, 149, 0, 0.8); }
  .btn-warning:focus, .btn-warning:active, .btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active {
    outline: none;
    color: #fff;
    background-color: rgba(255, 149, 0, 0.65);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-danger.dropdown-toggle {
  background-color: rgba(255, 149, 0, 0.65);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-info {
  background-color: #5ac8fa; }
  .btn-info:hover {
    background-color: rgba(90, 200, 250, 0.8); }
  .btn-info:focus, .btn-info:active, .btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(90, 200, 250, 0.65);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-info.dropdown-toggle {
  background-color: rgba(90, 200, 250, 0.65);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-light {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7); }
  .btn-light:hover {
    background-color: #e5e5e5;
    color: rgba(0, 0, 0, 0.7); }
  .btn-light:focus, .btn-light:active, .btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active {
    color: rgba(0, 0, 0, 0.7);
    outline: none;
    background-color: #e5e5e5;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-light.dropdown-toggle {
  color: rgba(0, 0, 0, 0.7);
  outline: none;
  background-color: #e5e5e5;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-dark {
  background-color: rgba(0, 0, 0, 0.95); }
  .btn-dark:hover {
    background-color: rgba(0, 0, 0, 0.7); }
  .btn-dark:focus, .btn-dark:active, .btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active {
    outline: none;
    background-color: rgba(0, 0, 0, 0.4);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.show > .btn-dark.dropdown-toggle {
  background-color: rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); }

.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-danger,
.btn-outline-warning,
.btn-outline-info,
.btn-outline-link,
.btn-outline-light,
.btn-outline-dark,
.btn-outline-disabled {
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 16px;
  font-weight: 600;
  padding: 0.43em 1.3em;
  border-radius: 7px;
  border: 2px solid #e5e5e5;
  box-shadow: none !important; }
  .btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active,
  .btn-outline-secondary:hover,
  .btn-outline-secondary:focus,
  .btn-outline-secondary:active,
  .btn-outline-success:hover,
  .btn-outline-success:focus,
  .btn-outline-success:active,
  .btn-outline-danger:hover,
  .btn-outline-danger:focus,
  .btn-outline-danger:active,
  .btn-outline-warning:hover,
  .btn-outline-warning:focus,
  .btn-outline-warning:active,
  .btn-outline-info:hover,
  .btn-outline-info:focus,
  .btn-outline-info:active,
  .btn-outline-link:hover,
  .btn-outline-link:focus,
  .btn-outline-link:active,
  .btn-outline-light:hover,
  .btn-outline-light:focus,
  .btn-outline-light:active,
  .btn-outline-dark:hover,
  .btn-outline-dark:focus,
  .btn-outline-dark:active,
  .btn-outline-disabled:hover,
  .btn-outline-disabled:focus,
  .btn-outline-disabled:active {
    border-color: #e5e5e5; }

.btn-outline-primary {
  color: #007aff;
  border-color: #007aff; }
  .btn-outline-primary:hover, .btn-outline-primary:active, .btn-outline-primary:focus, .btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active {
    color: #007aff;
    background-color: rgba(0, 122, 255, 0.1);
    border-color: #007aff; }

.btn-outline-secondary {
  color: #8e8e93;
  border-color: #8e8e93; }
  .btn-outline-secondary:hover, .btn-outline-secondary:active, .btn-outline-secondary:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active {
    color: #8e8e93;
    background-color: rgba(142, 142, 147, 0.1);
    border-color: #8e8e93; }

.btn-outline-success {
  color: #34c759;
  border-color: #34c759; }
  .btn-outline-success:hover, .btn-outline-success:active, .btn-outline-success:focus, .btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active {
    color: #34c759;
    background-color: rgba(52, 199, 89, 0.1);
    border-color: #34c759; }

.btn-outline-danger {
  color: #ff3b30;
  border-color: #ff3b30; }
  .btn-outline-danger:hover, .btn-outline-danger:active, .btn-outline-danger:focus, .btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active {
    color: #ff3b30;
    background-color: rgba(255, 59, 48, 0.1);
    border-color: #ff3b30; }

.btn-outline-warning {
  color: #ff9500;
  border-color: #ff9500; }
  .btn-outline-warning:hover, .btn-outline-warning:active, .btn-outline-warning:focus, .btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active {
    color: #ff9500;
    background-color: rgba(255, 149, 0, 0.1);
    border-color: #ff9500; }

.btn-outline-info {
  color: #5ac8fa;
  border-color: #5ac8fa; }
  .btn-outline-info:hover, .btn-outline-info:active, .btn-outline-info:focus, .btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active {
    color: #5ac8fa;
    background-color: rgba(90, 200, 250, 0.1);
    border-color: #5ac8fa; }

.btn-outline-light {
  color: rgba(0, 0, 0, 0.7);
  border-color: #e5e5e5; }
  .btn-outline-light:hover, .btn-outline-light:active, .btn-outline-light:focus, .btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active {
    color: rgba(0, 0, 0, 0.7);
    background-color: rgba(0, 0, 0, 0.05);
    border-color: #e5e5e5; }

.btn-outline-dark {
  color: rgba(0, 0, 0, 0.95);
  border-color: rgba(0, 0, 0, 0.95); }
  .btn-outline-dark:hover, .btn-outline-dark:active, .btn-outline-dark:focus, .btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active {
    color: rgba(0, 0, 0, 0.95);
    background-color: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.95); }

.btn-outline-disabled {
  color: rgba(0, 0, 0, 0.4); }
  .btn-outline-disabled:hover {
    color: rgba(0, 0, 0, 0.4); }

.btn-lg,
.btn-group-lg > .btn {
  padding: 0.72em 2.1em;
  font-size: 16px; }

.btn-lg.btn-primary,
.btn-group-lg > .btn.btn-primary {
  padding: 0.773em 1.6em; }

.btn-sm,
.btn-group-sm > .btn .btn-sm.btn-primary,
.btn-group-sm > .btn.btn-primary {
  font-size: 14px;
  padding: 0.5em 1.2em; }

.btn-block + .btn-block {
  margin-top: 10px; }

.btn-group .btn,
.btn-group-vertical .btn {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }
.btn-group .btn-primary:not(:last-child),
.btn-group .btn-blue-gray:not(:last-child),
.btn-group .btn-secondary:not(:last-child),
.btn-group .btn-success:not(:last-child),
.btn-group .btn-danger:not(:last-child),
.btn-group .btn-warning:not(:last-child),
.btn-group .btn-info:not(:last-child),
.btn-group .btn-dark:not(:last-child),
.btn-group-vertical .btn-primary:not(:last-child),
.btn-group-vertical .btn-blue-gray:not(:last-child),
.btn-group-vertical .btn-secondary:not(:last-child),
.btn-group-vertical .btn-success:not(:last-child),
.btn-group-vertical .btn-danger:not(:last-child),
.btn-group-vertical .btn-warning:not(:last-child),
.btn-group-vertical .btn-info:not(:last-child),
.btn-group-vertical .btn-dark:not(:last-child) {
  margin-left: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.3) !important; }
.btn-group .btn-light:not(:last-child),
.btn-group-vertical .btn-light:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.2); }

.btn-group > .btn:not(:first-child) {
  margin-left: -2px; }

.btn-group > .btn-group:not(:first-child),
.btn-group > .btn-primary:not(:first-child),
.btn-group > .btn-blue-gray:not(:first-child),
.btn-group > .btn-secondary:not(:first-child),
.btn-group > .btn-danger:not(:first-child),
.btn-group > .btn-warning:not(:first-child),
.btn-group > .btn-info:not(:first-child),
.btn-group > .btn-dark:not(:first-child),
.btn-group > .btn-light:not(:first-child) {
  margin-left: 0; }

.btn-outline-link:hover {
  background-color: rgba(0, 0, 0, 0.05); }

.alert {
  font-size: 14px;
  font-weight: 400;
  padding: 14px 15px;
  line-height: 1.45em;
  border: none;
  position: relative;
  border: 1px solid;
  border-radius: 7px; }

.alert-link {
  font-size: inherit;
  text-transform: uppercase;
  font-weight: 600;
  position: absolute;
  right: 15px; }

.alert-primary {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
  border-color: rgba(0, 122, 255, 0.3); }

.alert-secondary {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93;
  border-color: rgba(142, 142, 147, 0.3); }

.alert-success {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border-color: rgba(52, 199, 89, 0.3); }
  .alert-success hr {
    border-color: rgba(52, 199, 89, 0.1); }

.alert-danger {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border-color: rgba(255, 59, 48, 0.3); }

.alert-warning {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500;
  border-color: rgba(255, 149, 0, 0.3); }

.alert-info {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5ac8fa;
  border-color: rgba(90, 200, 250, 0.3); }

.alert-light {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7);
  border-color: rgba(0, 0, 0, 0.3); }

.alert-dark {
  background-color: rgba(0, 0, 0, 0.95);
  color: #fff; }

.alert-primary .alert-link {
  color: #007aff; }
  .alert-primary .alert-link:hover {
    color: rgba(0, 122, 255, 0.8); }

.alert-secondary .alert-link {
  color: #8e8e93; }
  .alert-secondary .alert-link:hover {
    color: rgba(142, 142, 147, 0.8); }

.alert-success .alert-link {
  color: #34c759; }
  .alert-success .alert-link:hover {
    color: rgba(52, 199, 89, 0.8); }

.alert-danger .alert-link {
  color: #ff3b30; }
  .alert-danger .alert-link:hover {
    color: rgba(255, 59, 48, 0.8); }

.alert-warning .alert-link {
  color: #ff9500; }
  .alert-warning .alert-link:hover {
    color: rgba(255, 149, 0, 0.8); }

.alert-info .alert-link {
  color: #5ac8fa; }
  .alert-info .alert-link:hover {
    color: rgba(90, 200, 250, 0.8); }

.alert-light .alert-link {
  color: rgba(0, 0, 0, 0.95); }
  .alert-light .alert-link:hover {
    color: rgba(0, 0, 0, 0.7); }

.alert-dark .alert-link {
  color: #007aff; }
  .alert-dark .alert-link:hover {
    color: rgba(0, 122, 255, 0.8); }

.alert hr {
  margin: 15px 0; }

.yoo-card.yoo-style1 {
  border-radius: 10px;
  background-color: #fff;
  border: 1px solid #e5e5e5; }
  .yoo-card.yoo-style1.yoo-height-auto {
    height: auto; }
  .yoo-card.yoo-style1 .yoo-card-heading {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    padding: 5px 20px;
    border-bottom: 1px solid #e5e5e5;
    min-height: 45px; }
  .yoo-card.yoo-style1 .yoo-card-title {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 0;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.7);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }
  .yoo-card.yoo-style1 .yoo-card-btn {
    background-color: transparent;
    border: none;
    font-size: 24px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0px 5px;
    margin-right: -8px;
    color: rgba(0, 0, 0, 0.7);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-card.yoo-style1 .yoo-card-btn:hover {
      color: rgba(0, 0, 0, 0.95); }
    .yoo-card.yoo-style1 .yoo-card-btn:focus {
      outline: none; }
  .yoo-card.yoo-style1 .yoo-card-heading .nav-tabs .nav-link {
    padding: 3px 35px; }

.card {
  border: none;
  border-radius: 4px;
  border: 1px solid #e5e5e5; }

.card-body {
  padding: 25px 20px; }

.card-text {
  font-size: 14px;
  line-height: 1.6em;
  margin-bottom: 14px; }

.card-title {
  margin-top: -3px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px; }

.card-subtitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87) !important;
  margin-top: 0;
  margin-bottom: 12px !important; }

.card-link + .card-link {
  margin-left: 0; }

.card-link {
  font-size: 15px;
  font-weight: 400;
  color: #007aff;
  padding: 7px 8px;
  border: none;
  margin-left: -8px;
  border-radius: 4px; }
  .card-link:hover {
    color: #007aff;
    background-color: rgba(0, 122, 255, 0.1); }

.card-header {
  padding: 15px 20px;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.05);
  border-color: #e5e5e5;
  font-size: 16px;
  line-height: 1.2em;
  color: rgba(0, 0, 0, 0.95); }

.card-footer {
  background-color: transparent;
  border-color: #e5e5e5;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 17px;
  font-weight: 400; }

.card-img-overlay {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5); }
  .card-img-overlay .card-title {
    color: #fff; }
  .card-img-overlay .card-text {
    color: rgba(255, 255, 255, 0.7); }

.text-white .card-header,
.text-white .card-title {
  color: #fff !important; }

.text-muted {
  color: rgba(0, 0, 0, 0.4) !important;
  font-size: 14px; }

.card-header-tabs {
  margin-right: -20px;
  margin-bottom: -16px;
  margin-left: -20px;
  margin-top: -15px;
  border-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  background: #fff;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #e5e5e5; }

.border-card {
  border: 1px solid;
  -webkit-box-shadow: none;
          box-shadow: none; }

.card-header.bg-primary, .card-header.bg-secondary, .card-header.bg-success, .card-header.bg-danger, .card-header.bg-warning, .card-header.bg-warning, .card-header.bg-info, .card-header.bg-dark {
  border: none; }

.yoo-card-title-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 22px;
  width: 22px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 5px;
  background: #007aff;
  color: #fff;
  margin-right: 6px;
  font-size: 14px; }

.yoo-card-meta.yoo-style1 .yoo-card-meta-title {
  line-height: 22px; }
.yoo-card-meta.yoo-style1 .yoo-card-meta-number {
  font-size: 24px;
  line-height: 28px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-card-meta.yoo-style1 .yoo-card-meta-number span {
    font-size: 18px;
    margin-left: 6px; }

@media screen and (max-width: 575px) {
  .yoo-card.yoo-style1 .yoo-card-heading .nav-tabs .nav-link {
    padding: 3px 15px; } }
@media screen and (max-width: 400px) {
  .yoo-card.yoo-style1 .yoo-card-heading .nav-tabs .nav-link {
    padding: 3px 10px; } }
.list-group-item {
  font-size: 16px;
  line-height: 1.6em;
  border-color: #e5e5e5;
  color: rgba(0, 0, 0, 0.95);
  padding: 12px 20px; }

.list-group-item.active,
.list-group-item-action.active:hover {
  border-color: rgba(0, 122, 255, 0.1);
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff; }

.list-group-item.disabled,
.list-group-item:disabled {
  color: rgba(0, 0, 0, 0.4); }

.list-group-item-action:focus,
.list-group-item-action:hover {
  color: #007aff;
  background-color: rgba(0, 122, 255, 0.1);
  border-color: rgba(0, 122, 255, 0.1); }

.list-group-item-primary {
  background-color: #007aff;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-secondary {
  background-color: #8e8e93;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-success {
  background-color: #34c759;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-danger {
  background-color: #ff3b30;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-warning {
  background-color: #ff9500;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-info {
  background-color: #5ac8fa;
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-light {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7);
  border: none;
  letter-spacing: 0.17px; }

.list-group-item-dark {
  background-color: rgba(0, 0, 0, 0.95);
  color: #fff;
  border: none;
  letter-spacing: 0.17px; }

.list-unstyled {
  list-style: disc;
  padding-left: 17px;
  margin-bottom: 20px; }
  .list-unstyled li {
    font-size: 16px;
    line-height: 1.6em;
    margin-bottom: 3px; }
    .list-unstyled li:last-child {
      margin-bottom: 0; }
  .list-unstyled ul {
    margin-top: 3px;
    list-style: circle; }

.list-inline {
  margin-bottom: 20px; }
  .list-inline li {
    font-size: 16px;
    line-height: 1.6em; }

.yoo-list-group.yoo-style1 li {
  margin-left: 45px; }
.yoo-list-group.yoo-style1 li:not(:last-child) {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
  margin-bottom: 10px; }
.yoo-list-group.yoo-style1.yoo-type1 li {
  margin-left: 63px; }

.yoo-list-group.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-list-group.yoo-style2 li:not(:last-child) {
    border-right: 1px solid #e5e5e5;
    margin-right: 20px;
    padding-right: 20px; }

.yoo-list-group.yoo-style3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-left: -8px;
  margin-right: -8px; }
  .yoo-list-group.yoo-style3 li {
    padding: 0 8px;
    width: 25%; }

.list-group-flush .list-group-item {
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  margin-left: -1px;
  margin-right: -1px; }

.list-group-flush .list-group-item:first-child {
  border-top-left-radius: .25rem;
  border-top-right-radius: .25rem; }

.list-group-flush .list-group-item:last-child {
  border-bottom-right-radius: .25rem;
  border-bottom-left-radius: .25rem; }

@media screen and (max-width: 575px) {
  .yoo-list-group.yoo-style3 li {
    width: 33.333333%; } }
.carousel-control-prev,
.carousel-control-next {
  font-size: 24px;
  opacity: 1;
  background-color: #fff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.7);
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%); }
  .carousel-control-prev:hover,
  .carousel-control-next:hover {
    color: rgba(0, 0, 0, 0.95); }
  .carousel-control-prev:focus,
  .carousel-control-next:focus {
    color: rgba(0, 0, 0, 0.7); }
  .carousel-control-prev i,
  .carousel-control-next i {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }

.carousel-control-prev {
  left: 30px; }

.carousel-control-next {
  right: 30px; }

.carousel-caption {
  bottom: 24px; }
  .carousel-caption h5 {
    color: #fff;
    margin-bottom: 5px; }
  .carousel-caption p {
    color: rgba(255, 255, 255, 0.7); }

.carousel-indicators {
  margin-bottom: 20px; }

.carousel-indicators li {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  margin: 0 5px; }

.carousel-control-prev,
.carousel-control-next {
  font-size: 22px;
  background-color: rgba(0, 0, 0, 0.6);
  width: 44px;
  height: 44px;
  color: #fff;
  border: 1px solid #fff;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.carousel-control-prev:hover,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-next:focus {
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff; }

.breadcrumb {
  padding: 13px 15px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05); }
  .breadcrumb .breadcrumb-item.active {
    color: rgba(0, 0, 0, 0.7); }
  .breadcrumb a {
    color: #007aff; }
    .breadcrumb a:hover {
      color: rgba(0, 122, 255, 0.8); }

.input-group label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 4px; }

.navbar .form-control {
  min-height: 20px; }

.form-control {
  border-color: #f0f0f0;
  max-height: 100px; }
  .form-control:focus {
    -webkit-box-shadow: none;
            box-shadow: none;
    border-color: #007aff;
    -webkit-box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    color: rgba(0, 0, 0, 0.7); }

.form-group {
  margin-bottom: 20px; }

.form-control {
  padding: 15px;
  min-height: 48px;
  color: rgba(0, 0, 0, 0.7);
  font-size: 16px;
  background-color: #f0f0f0;
  border-radius: 7px; }

.level-up .form-control {
  padding: 23px 15px 10px; }

.level-up textarea.form-control {
  padding: 20px 15px 13px; }

.level-up .text-muted {
  color: rgba(0, 0, 0, 0.7) !important;
  font-size: 12px;
  margin-top: 3px;
  margin-bottom: -5px;
  line-height: 1.6em;
  padding: 0 20px; }

.level-up {
  position: relative; }

.level-up label {
  position: absolute;
  margin: 0;
  font-size: 16px;
  left: 15px;
  top: 13px;
  color: rgba(0, 0, 0, 0.7);
  padding: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }

.level-up.active1 label,
.level-up.active2 label {
  font-size: 11px;
  top: 1px; }

.level-up.active2 label {
  color: rgba(0, 0, 0, 0.4); }

.form-group-file {
  position: relative; }
  .form-group-file:before {
    content: '';
    position: absolute;
    height: calc(100% - 2px);
    width: 148px;
    background-color: #fff;
    top: 1px;
    left: 5px; }
  .form-group-file label {
    position: absolute;
    font-size: 14px;
    background: #fff;
    top: 16px;
    padding: 5px 18px;
    left: 19px;
    border: 1px solid #007aff;
    border-radius: 4px;
    background-color: #007aff;
    text-transform: uppercase;
    color: #fff;
    font-weight: 600;
    margin-bottom: 0; }
  .form-group-file input {
    border: 1px solid #e5e5e5;
    padding: 16px 18px;
    min-height: 66px;
    border-radius: 4px;
    padding-left: 55px; }
    .form-group-file input:focus {
      outline: none; }

.form-group-md label {
  font-size: 14px;
  left: 15px;
  top: 9px; }
.form-group-md .form-control {
  padding: 12px 15px;
  min-height: 42px;
  font-size: 14px; }
.form-group-md.level-up .form-control {
  padding: 17px 15px 8px; }

.form-group-md.level-up.active1 label,
.form-group-md.level-up.active2 label {
  font-size: 11px;
  top: 0px; }

.form-group-sm label {
  font-size: 13px;
  left: 15px;
  top: 6px; }
.form-group-sm .form-control {
  padding: 5px 15px;
  min-height: 36px;
  font-size: 14px; }
.form-group-sm.level-up.active1 label, .form-group-sm.level-up.active2 label {
  font-size: 10px;
  top: -2px; }

.form-group-sm.level-up .form-control {
  padding: 14px 15px 6px;
  font-size: 13px; }

.form-control:disabled,
.form-control[readonly] {
  background-color: #f0f0f0;
  color: rgba(0, 0, 0, 0.4); }

.form-control[readonly]::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-control[readonly]::-moz-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-control[readonly]:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-control[readonly]:-moz-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-row > .col,
.form-row > [class*=col-] {
  padding-right: 10px;
  padding-left: 10px; }

.col-form-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.95);
  padding: 15px 15px; }

.input-group-text {
  background-color: #e2e2e2;
  border-color: #d8d8d8; }

.input-group-prepend .input-group-text {
  border-right-width: 2px; }

.input-group-append .input-group-text {
  border-left-width: 2px; }

.form-group-sm .input-group-text {
  font-size: 13px; }

.input-group-append .btn,
.input-group-prepend .btn {
  border-width: 1px; }

.input-group-prepend [class*=btn-outline-],
.input-group-append [class*=btn-outline-] {
  border-color: #e5e5e5;
  color: rgba(0, 0, 0, 0.7); }
  .input-group-prepend [class*=btn-outline-]:hover,
  .input-group-append [class*=btn-outline-]:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: rgba(0, 0, 0, 0.95); }

.custom-radio .custom-control-input,
.custom-checkbox .custom-control-input {
  position: absolute;
  z-index: 2;
  opacity: 0;
  left: 0;
  top: 0;
  height: 22px;
  width: 22px; }

.custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 30px; }

.custom-control-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.95); }

.custom-control-label::before {
  top: 2px;
  left: -30px;
  height: 20px;
  width: 20px;
  border-color: #DBDBDB;
  border-width: 1px;
  z-index: 1; }

.custom-control-label::after {
  top: 4px;
  left: -24px;
  height: 12px;
  width: 6px;
  border-radius: 0;
  border: 2px solid #fff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  border-left: none;
  border-top: none;
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  z-index: 1; }

.custom-checkbox .custom-control-label::before {
  border-radius: 6px; }

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
  opacity: 1; }

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: none;
  opacity: 1; }

.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background-color: transparent; }

.custom-radio .custom-control-label::after {
  border: none;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: #007aff;
  left: -25px;
  top: 7px; }

.custom-radio .custom-control-label::before {
  height: 20px;
  width: 20px; }

.custom-control-input:checked ~ .custom-control-label::before {
  border-color: #007aff;
  background-color: #007aff; }

.custom-control-input:disabled ~ .custom-control-label {
  color: rgba(0, 0, 0, 0.95); }

.custom-control.form-check-inline {
  display: inline-block;
  margin-right: 30px; }

.custom-control.no-labels {
  margin-right: 0;
  padding-left: 18px; }

.custom-control.no-labels .custom-control-label::before {
  left: -18px; }

.custom-control.no-labels .custom-control-label::after {
  left: -11px; }

.input-group-text {
  padding: 5px 15px; }

.custom-control.no-labels.custom-radio {
  padding-left: 20px; }

.custom-control.no-labels.custom-radio .custom-control-label::before {
  left: -20px; }

.custom-control.no-labels.custom-radio .custom-control-label::after {
  left: -15px; }

.custom-control.no-labels.yoo-style1 .hydrated {
  position: absolute;
  left: -20px;
  z-index: 1;
  font-size: 24px;
  top: 0px;
  color: rgba(0, 0, 0, 0.4); }

.custom-control.no-labels.yoo-style1 .custom-control-label::before {
  border-color: transparent;
  height: 20px;
  width: 20px; }

.custom-control.no-labels.yoo-style1 .custom-control-input:checked ~ .custom-control-label::before {
  border-color: #007aff;
  background-color: #007aff; }

.custom-control.no-labels.yoo-style1 .custom-control-input:checked ~ .custom-control-label .hydrated {
  color: #007aff; }

.custom-control.no-labels.yoo-style1 .custom-control-label::after {
  left: -11px;
  top: 4px; }

.form-row {
  margin-right: -10px;
  margin-left: -10px; }

.yoo-select {
  position: relative; }
  .yoo-select .form-control {
    padding-left: 15px; }

.yoo-select:before {
  content: '';
  position: absolute;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.4);
  right: 20px;
  top: 23px;
  z-index: 1;
  pointer-events: none; }

.yoo-select:after {
  content: '';
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: #f0f0f0;
  top: 17px;
  right: 3px;
  pointer-events: none; }

.navbar .form-control {
  background-color: #fff;
  height: 42px; }

.form-group-sm .yoo-select:after {
  top: 13px; }
.form-group-sm .yoo-select:before {
  top: 15px; }

.dropdown-menu {
  padding: 10px 0;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: none; }

.dropdown-item {
  font-size: 14px;
  padding: 10px 15px;
  line-height: 1.6em;
  color: rgba(0, 0, 0, 0.95); }

.dropdown-item:focus,
.dropdown-item:hover {
  color: #007aff;
  text-decoration: none;
  background-color: rgba(0, 122, 255, 0.1); }

.dropdown-divider {
  border-color: #e5e5e5;
  margin: 10px 0; }

.dropdown-toggle:after,
.dropleft .dropdown-toggle:before {
  font-size: 18px;
  position: relative;
  top: 2px; }

.dropdown-toggle:after {
  margin-left: 6px; }

.dropleft .dropdown-toggle:before {
  margin-right: 6px; }

.dropdown-toggle-split.dropdown-toggle:after {
  margin-left: 0; }

.dropleft .dropdown-toggle-split.dropdown-toggle:before {
  margin-right: 0; }

.dropdown-toggle.btn-sm:after,
.dropleft .dropdown-toggle.btn-sm:before {
  top: 2px;
  font-size: 16px; }

.dropdown-toggle.btn-lg:after,
.dropleft .dropdown-toggle.btn-lg:before {
  top: 2px;
  font-size: 20px; }

.btn-lg + .dropdown-menu {
  width: 220px; }

.btn-sm + .dropdown-menu .dropdown-item {
  padding: 7px 15px; }

.jumbotron {
  padding: 50px 40px;
  background-color: rgba(0, 0, 0, 0.05); }

.display-4 {
  font-size: 60px;
  letter-spacing: -0.5px;
  line-height: 1.2em;
  margin-bottom: 7px; }

.lead {
  font-size: 20px;
  line-height: 1.6em;
  letter-spacing: 0.25px; }

.scrollspy-example {
  position: relative;
  height: 200px;
  margin-top: .5rem;
  overflow: auto; }

.scrollspy-example-2 {
  position: relative;
  height: 378px;
  overflow: auto; }

.tooltip {
  line-height: 1.6em;
  font-size: 12;
  font-family: inherit; }

.tooltip.show {
  opacity: 1; }

.tooltip-inner {
  max-width: 200px;
  padding: 5px 15px;
  color: #fff;
  text-align: center;
  background-color: #8e8e93;
  border-radius: 4px; }

.bs-tooltip-auto[x-placement^=top] .arrow::before,
.bs-tooltip-top .arrow::before {
  border-top-color: #8e8e93; }

.bs-tooltip-auto[x-placement^=right] .arrow::before,
.bs-tooltip-right .arrow::before {
  border-right-color: #8e8e93; }

.bs-tooltip-auto[x-placement^=bottom] .arrow::before,
.bs-tooltip-bottom .arrow::before {
  border-bottom-color: #8e8e93; }

.bs-tooltip-auto[x-placement^=left] .arrow::before,
.bs-tooltip-left .arrow::before {
  border-left-color: #8e8e93; }

.progress {
  height: 17px;
  border-radius: 4px;
  background-color: #e5e5e5;
  overflow: hidden; }

.with-lables.progress .progress-bar {
  border-radius: initial; }

.progress-bar {
  background-color: #007aff;
  position: relative;
  border-radius: 4px; }

.progress span {
  position: relative;
  z-index: 1;
  color: #fff; }

.yoo-progress-wrap.yoo-style3 .yoo-progress-head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 3px; }
.yoo-progress-wrap.yoo-style3 .yoo-progressbar-title {
  font-weight: 600;
  font-size: 13px;
  line-height: 22px; }
.yoo-progress-wrap.yoo-style3 .yoo-progressbar-percentage {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 22px; }
.yoo-progress-wrap.yoo-style3 .progress {
  height: 4px; }
.yoo-progress-wrap.yoo-style3 .progress-bar {
  opacity: 0.7; }
.yoo-progress-wrap.yoo-style3.yoo-type1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-progress-wrap.yoo-style3.yoo-type1 .yoo-progress-head {
    margin: 0;
    width: 35px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
  .yoo-progress-wrap.yoo-style3.yoo-type1 .yoo-progressbar-percentage {
    color: rgba(0, 0, 0, 0.7); }
  .yoo-progress-wrap.yoo-style3.yoo-type1 .progress {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }

.yoo-progressbar-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: calc(100% - 20px); }
  .yoo-progressbar-wrap.yoo-style1 .yoo-progressbar {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    width: 50%;
    height: 5px;
    background-color: #e5e5e5;
    border-radius: 3px; }
  .yoo-progressbar-wrap.yoo-style1 .yoo-progressbar-number {
    font-size: 13px;
    line-height: 1.2em;
    color: rgba(0, 0, 0, 0.4);
    margin-left: 6px;
    line-height: 15px; }
  .yoo-progressbar-wrap.yoo-style1 .progress-bar {
    opacity: 0.7; }

.yoo-progressbar-wrap.yoo-style2 .yoo-progressbar-title {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 1.6em;
  margin-bottom: 2px;
  text-transform: uppercase; }
.yoo-progressbar-wrap.yoo-style2 .yoo-progressbar-number {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.95);
  line-height: 1.2em;
  margin-bottom: 10px; }
.yoo-progressbar-wrap.yoo-style2 .yoo-progressbar-out {
  height: 4px;
  border-radius: 12px;
  overflow: hidden;
  background-color: #e5e5e5; }
  .yoo-progressbar-wrap.yoo-style2 .yoo-progressbar-out .yoo-progressbar {
    height: 100%;
    background-color: #8e8e93;
    opacity: 0.7; }

.popover {
  font-family: 'Inter', sans-serif; }

.popover-header {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.95);
  border-color: #e5e5e5;
  padding: 10px 15px;
  font-weight: 500; }

.popover {
  border-color: #e5e5e5; }

.popover-body {
  padding: 10px 15px;
  color: rgba(0, 0, 0, 0.7); }

.page-item .page-link {
  color: #007aff; }
  .page-item .page-link span {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; }

.page-link {
  color: rgba(0, 0, 0, 0.7);
  border-color: #e5e5e5;
  background-color: #fff;
  padding: 7px 10px;
  font-size: 14px;
  height: 36px;
  min-width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.page-item.disabled .page-link {
  background-color: #fff;
  border-color: #e5e5e5; }

.page-link:focus,
.page-link:hover {
  color: #007aff;
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.05);
  border-color: #e5e5e5; }

.page-item.active .page-link {
  background-color: #007aff;
  border-color: #007aff; }

.pagination i,
.pagination .hydrated {
  line-height: 1em;
  font-size: 18px; }

.page-link:focus {
  -webkit-box-shadow: none;
          box-shadow: none; }

.pagination-lg .page-link {
  padding: 10px 16px;
  height: 42px;
  font-size: 16px; }

.pagination-sm .page-link {
  padding: 5px 11px;
  font-size: 13px;
  height: 30px; }

.page-item:first-child .page-link {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.page-item:last-child .page-link {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.modal.modal-comp {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  display: block; }

.modal-header .close {
  font-size: 24px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .modal-header .close:focus {
    outline: none;
    -webkit-box-shadow: none;
            box-shadow: none; }

.modal-content {
  border-color: #e5e5e5; }

.modal-btn-primary,
.modal-btn-secondary,
.modal-btn-success,
.modal-btn-danger,
.modal-btn-warning,
.modal-btn-info {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  padding: 0.54em 1.12em;
  letter-spacing: 0.89px;
  border: none;
  margin-left: -10px;
  border-radius: 4px; }
  .modal-btn-primary:hover,
  .modal-btn-secondary:hover,
  .modal-btn-success:hover,
  .modal-btn-danger:hover,
  .modal-btn-warning:hover,
  .modal-btn-info:hover {
    color: #007aff;
    background-color: rgba(0, 122, 255, 0.1); }

.modal-btn-primary {
  color: #007aff; }
  .modal-btn-primary:hover {
    color: #007aff;
    background-color: rgba(0, 122, 255, 0.1); }

.modal-btn-secondary {
  color: #8e8e93; }
  .modal-btn-secondary:hover {
    color: #8e8e93;
    background-color: rgba(142, 142, 147, 0.1); }

.modal-btn-success {
  color: #34c759; }
  .modal-btn-success:hover {
    color: #34c759;
    background-color: rgba(52, 199, 89, 0.1); }

.modal-btn-danger {
  color: #ff3b30; }
  .modal-btn-danger:hover {
    color: #ff3b30;
    background-color: rgba(255, 59, 48, 0.1); }

.modal-btn-warning {
  color: #ff9500; }
  .modal-btn-warning:hover {
    color: #ff9500;
    background-color: rgba(255, 149, 0, 0.1); }

.modal-btn-info {
  color: #5ac8fa; }
  .modal-btn-info:hover {
    color: #5ac8fa;
    background-color: rgba(90, 200, 250, 0.1); }

.modal-btn-dark {
  color: rgba(0, 0, 0, 0.95); }
  .modal-btn-dark:hover {
    color: rgba(0, 0, 0, 0.95);
    background-color: rgba(0, 0, 0, 0.1); }

.badge-primary {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff; }

.badge-secondary {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93; }

.badge-success {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759; }

.badge-danger {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30; }

.badge-warning {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500; }

.badge-info {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5ac8fa; }

.badge-light {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.7); }

.badge-dark {
  color: rgba(0, 0, 0, 0.95);
  background-color: rgba(0, 0, 0, 0.1); }

.btn .badge-primary {
  background-color: #007aff;
  color: #fff; }
.btn .badge-secondary {
  background-color: #8e8e93;
  color: #fff; }
.btn .badge-success {
  background-color: #34c759;
  color: #fff; }
.btn .badge-danger {
  background-color: #ff3b30;
  color: #fff; }
.btn .badge-warning {
  background-color: #ff9500;
  color: #fff; }
.btn .badge-info {
  background-color: #5ac8fa;
  color: #fff; }
.btn .badge-dark {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.95); }

.badge {
  font-size: 0.75em;
  font-weight: 400;
  line-height: 1em;
  padding: 0.5em 0.58em;
  border-radius: 4px;
  text-transform: uppercase; }

.badge.badge-pill {
  border-radius: 12px;
  text-transform: initial;
  padding: 0.5em 0.7em; }

h1 .badge,
h2 .badge,
h3 .badge,
h4 .badge,
h5 .badge,
h6 .badge {
  font-weight: inherit;
  padding: 0.25em 0.5em; }

.up-badge {
  position: relative; }
  .up-badge .badge {
    min-width: 25px;
    border-radius: 50%;
    font-size: 14px;
    position: absolute;
    right: -10px;
    top: -10px;
    padding: 0.4em 0.4em; }

.yoo-table {
  overflow-x: auto;
  overflow-y: hidden; }

.table thead th {
  border-bottom-width: 1px;
  border-color: #e5e5e5;
  color: rgba(0, 0, 0, 0.95);
  font-weight: 600; }

.table td,
.table th {
  border-color: #e5e5e5;
  vertical-align: middle; }

.table .thead-light th {
  color: rgba(0, 0, 0, 0.95);
  background-color: rgba(0, 0, 0, 0.05);
  border-color: #e5e5e5; }

.table .thead-dark th {
  background-color: rgba(0, 0, 0, 0.95); }

.table-hover tbody tr:hover {
  color: rgba(0, 0, 0, 0.95);
  background-color: rgba(0, 0, 0, 0.05); }

.table-primary,
.table-primary > td,
.table-primary > th {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff; }

.table-secondary,
.table-secondary > td,
.table-secondary > th {
  background-color: #8e8e93;
  color: #fff;
  border-color: #8e8e93; }

.table-success,
.table-success > td,
.table-success > th {
  background-color: #34c759;
  color: #fff;
  border-color: #34c759; }

.table-danger,
.table-danger > td,
.table-danger > th {
  background-color: #ff3b30;
  color: #fff;
  border-color: #ff3b30; }

.table-warning,
.table-warning > td,
.table-warning > th {
  background-color: #ff9500;
  color: #fff;
  border-color: #ff9500; }

.table-info,
.table-info > td,
.table-info > th {
  background-color: #5ac8fa;
  color: #fff;
  border-color: #5ac8fa; }

.table-light,
.table-light > td,
.table-light > th {
  background-color: rgba(0, 0, 0, 0.05); }

.table-dark,
.table-dark > td,
.table-dark > th {
  background-color: rgba(0, 0, 0, 0.95);
  border-color: rgba(0, 0, 0, 0.95);
  color: #fff; }

.yoo-table-heading.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-table-heading.yoo-style1 .btn {
    border-radius: 4px; }
    .yoo-table-heading.yoo-style1 .btn .yoo-add {
      font-size: 16px;
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex; }
  .yoo-table-heading.yoo-style1 .btn-outline-light {
    border-width: 1px;
    background-color: rgba(0, 0, 0, 0.05); }
  .yoo-table-heading.yoo-style1 .show > .btn-outline-light.dropdown-toggle {
    border-color: #e5e5e5; }
  .yoo-table-heading.yoo-style1 .page-item .page-link {
    color: rgba(0, 0, 0, 0.7); }
  .yoo-table-heading.yoo-style1 .page-item.active .page-link {
    background-color: transparent;
    border-color: #e5e5e5;
    color: rgba(0, 0, 0, 0.4); }

.yoo-table-heading-btn-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-table-heading-btn-list > li:not(:last-child) {
    margin-right: 10px; }

.yoo-table.yoo-style1 {
  font-size: 14px; }
  .yoo-table.yoo-style1 .yoo-task-checkmark {
    width: 20px; }
  .yoo-table.yoo-style1 .table {
    margin-bottom: 0;
    border: none; }
    .yoo-table.yoo-style1 .table tr:last-child td {
      border-bottom: none; }
    .yoo-table.yoo-style1 .table td:first-child,
    .yoo-table.yoo-style1 .table th:first-child {
      border-left: none;
      width: 45px; }
    .yoo-table.yoo-style1 .table td:last-child,
    .yoo-table.yoo-style1 .table th:last-child {
      border-right: none;
      width: 60px;
      text-align: center; }
    .yoo-table.yoo-style1 .table th {
      background-color: rgba(0, 0, 0, 0.05);
      color: rgba(0, 0, 0, 0.7); }
  .yoo-table.yoo-style1 .table td,
  .yoo-table.yoo-style1 .table th {
    padding: 10px 20px; }
  .yoo-table.yoo-style1.yoo-type1 .table th {
    border-top: none;
    background-color: transparent; }
    .yoo-table.yoo-style1.yoo-type1 .table th:first-child {
      border-top-left-radius: 10px; }
    .yoo-table.yoo-style1.yoo-type1 .table th:last-child {
      border-top-right-radius: 10px; }
  .yoo-table.yoo-style1.yoo-type1 .table td:last-child {
    text-align: left; }
  .yoo-table.yoo-style1.yoo-type2 .yoo-switch {
    margin: auto; }
  .yoo-table.yoo-style1.yoo-type2 tr td:last-child,
  .yoo-table.yoo-style1.yoo-type2 tr th:last-child {
    width: 1px; }

.yoo-table-action-btn.yoo-style1 {
  border: none;
  height: 26px;
  width: 26px;
  padding: 0;
  border-radius: 50%;
  font-size: 18px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(0, 0, 0, 0.7);
  background-color: rgba(0, 0, 0, 0.05); }
  .yoo-table-action-btn.yoo-style1:focus {
    outline: none; }

.yoo-table-info-btn.yoo-style1 {
  padding: 0;
  margin: 0;
  margin-left: 2px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border: none;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
  position: relative;
  top: 4px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-table-info-btn.yoo-style1:focus {
    outline: none; }
  .yoo-table-info-btn.yoo-style1:hover {
    color: rgba(0, 0, 0, 0.7); }

.yoo-table-medias.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-table-medias.yoo-style1 .yoo-media-img {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    margin-right: 10px; }
  .yoo-table-medias.yoo-style1 .yoo-media-title {
    font-size: 14px;
    font-weight: 400; }
  .yoo-table-medias.yoo-style1 a {
    color: #007aff; }
    .yoo-table-medias.yoo-style1 a:hover {
      text-decoration: underline; }

.yoo-table-chart {
  width: 120px;
  margin-top: -30px;
  margin-bottom: -30px;
  margin-left: -20px;
  margin-right: -10px; }

.dropdown-menu.yoo-table-info-text {
  width: 300px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.72);
  font-weight: 400;
  padding: 15px 20px; }

.yoo-arrow-wrap {
  position: relative; }

.yoo-arrow-but-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%); }
  .yoo-arrow-but-group .yoo-arrow-btn {
    padding: 0;
    margin: -2px 0;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    border: none;
    font-size: 12px;
    background: transparent;
    color: rgba(0, 0, 0, 0.4);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-arrow-but-group .yoo-arrow-btn:hover {
      color: rgba(0, 0, 0, 0.7); }
    .yoo-arrow-but-group .yoo-arrow-btn:focus {
      outline: none; }

.yoo-filter-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: absolute;
  right: 12px;
  top: 12px;
  padding: 0;
  border: none;
  cursor: pointer;
  font-size: 18px;
  background: transparent;
  color: rgba(0, 0, 0, 0.4);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-filter-btn:hover {
    color: rgba(0, 0, 0, 0.7); }
  .yoo-filter-btn:focus {
    outline: none; }

.yoo-arrow-wrap {
  position: relative; }
  .yoo-arrow-wrap .yoo-filter-btn {
    opacity: 0; }
  .yoo-arrow-wrap:hover .yoo-filter-btn {
    opacity: 1; }

@media screen and (max-width: 1500px) {
  .yoo-table.yoo-style1 .table td,
  .yoo-table.yoo-style1 .table th {
    padding: 10px 15px; } }
.toast {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #e5e5e5; }

.toast .close {
  font-size: 20px;
  line-height: 1em;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: rgba(0, 0, 0, 0.7);
  position: relative;
  top: 3px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .toast .close:hover {
    color: rgba(0, 0, 0, 0.95); }

.toast strong {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.95); }

.toast-header {
  padding: 10px 15px;
  border-color: #e5e5e5; }

.toast-body {
  padding: 10px 15px; }

.toast-header small {
  color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
  line-height: 1.6em; }

.yoo-iconbox.yoo-style1 {
  border-radius: 10px;
  min-height: 140px; }
  .yoo-iconbox.yoo-style1 .yoo-iconbox-in {
    padding: 20px;
    position: relative;
    min-height: inherit;
    border-radius: inherit;
    background: -webkit-gradient(linear, right top, left top, from(rgba(255, 255, 255, 0)), to(rgba(255, 244, 240, 0.3)));
    background: linear-gradient(to left, rgba(255, 255, 255, 0) 0%, rgba(255, 244, 240, 0.3) 100%); }
  .yoo-iconbox.yoo-style1 .yoo-iconbox-title {
    font-size: 12px;
    text-transform: uppercase;
    line-height: 1.6em;
    margin-top: -5px; }
  .yoo-iconbox.yoo-style1 .yoo-iconbox-number {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2em; }
  .yoo-iconbox.yoo-style1 .yoo-iconbox-footer {
    position: absolute;
    bottom: 14px;
    left: 20px;
    font-size: 14px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }
    .yoo-iconbox.yoo-style1 .yoo-iconbox-footer .yoo-iconbox-percentage {
      font-weight: 600;
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      margin-right: 10px; }
      .yoo-iconbox.yoo-style1 .yoo-iconbox-footer .yoo-iconbox-percentage i,
      .yoo-iconbox.yoo-style1 .yoo-iconbox-footer .yoo-iconbox-percentage .hydrated {
        font-size: 18px;
        position: relative;
        top: 2px;
        margin-right: 2px; }
  .yoo-iconbox.yoo-style1 .yoo-iconbox-icon {
    position: absolute;
    right: 20px;
    top: 15px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 40px;
    line-height: 1em; }
  .yoo-iconbox.yoo-style1.yoo-color1 {
    background-color: #007aff; }
    .yoo-iconbox.yoo-style1.yoo-color1 .yoo-iconbox-title {
      color: rgba(255, 255, 255, 0.7); }
    .yoo-iconbox.yoo-style1.yoo-color1 .yoo-iconbox-number {
      color: #fff; }
    .yoo-iconbox.yoo-style1.yoo-color1 .yoo-iconbox-footer {
      color: rgba(255, 255, 255, 0.7); }
      .yoo-iconbox.yoo-style1.yoo-color1 .yoo-iconbox-footer .yoo-iconbox-percentage {
        color: #fff; }
    .yoo-iconbox.yoo-style1.yoo-color1 .yoo-iconbox-icon {
      color: #fff; }
  .yoo-iconbox.yoo-style1.yoo-color2 {
    background-color: #fff; }
    .yoo-iconbox.yoo-style1.yoo-color2 .yoo-iconbox-in {
      background: inherit; }
    .yoo-iconbox.yoo-style1.yoo-color2 .yoo-iconbox-title {
      color: rgba(0, 0, 0, 0.4); }
    .yoo-iconbox.yoo-style1.yoo-color2 .yoo-iconbox-number {
      color: rgba(0, 0, 0, 0.95); }
    .yoo-iconbox.yoo-style1.yoo-color2 .yoo-iconbox-icon {
      color: rgba(0, 0, 0, 0.4); }
  .yoo-iconbox.yoo-style1.yoo-type1 .yoo-iconbox-footer {
    left: initial;
    right: 20px; }
  .yoo-iconbox.yoo-style1.yoo-type1 .yoo-iconbox-chart {
    max-width: 150px;
    margin-top: -30px;
    margin-bottom: -15px;
    margin-left: auto;
    margin-right: -10px; }
  .yoo-iconbox.yoo-style1.yoo-type1 .yoo-iconbox-icon {
    right: initial;
    left: 20px;
    bottom: 14px;
    top: initial; }

.yoo-medias.yoo-style1 {
  min-height: 30px;
  position: relative; }
  .yoo-medias.yoo-style1 .yoo-media-img {
    position: absolute;
    left: -45px;
    width: 30px; }
  .yoo-medias.yoo-style1 .yoo-media-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 16px; }
  .yoo-medias.yoo-style1 .yoo-media-subtitle {
    color: rgba(0, 0, 0, 0.4); }
  .yoo-medias.yoo-style1 .yoo-media-tag {
    position: absolute;
    right: 0;
    top: 8px;
    font-size: 13px;
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    line-height: 1.6em;
    padding: 2px 8px;
    color: #007aff; }
  .yoo-medias.yoo-style1 .yoo-groth-percentage {
    position: absolute;
    top: 4px;
    right: 0; }
  .yoo-medias.yoo-style1 .badge {
    position: absolute;
    top: 10px;
    right: 0; }
  .yoo-medias.yoo-style1 .yoo-media-text {
    margin-top: 1px;
    line-height: 22px; }
  .yoo-medias.yoo-style1.yoo-type1 {
    min-height: 48px; }
    .yoo-medias.yoo-style1.yoo-type1 .yoo-media-img {
      position: absolute;
      left: -63px;
      width: 48px; }

.yoo-medias.yoo-style2 .yoo-media-title {
  margin-top: 2px;
  margin-bottom: -2px;
  line-height: 16px; }

@media screen and (max-width: 1500px) {
  .yoo-medias.yoo-style1 .yoo-media-text {
    max-height: 44px;
    overflow: hidden; } }
.yoo-chart-wrap.yoo-style1 {
  margin-left: -7px;
  margin-right: -4px; }
  .yoo-chart-wrap.yoo-style1 .yoo-chart-in {
    height: 202px;
    width: 100%; }
  .yoo-chart-wrap.yoo-style1 canvas {
    height: 100% !important; }

.yoo-porductivity-chart {
  height: 120px;
  width: 100%;
  max-width: 380px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.apexcharts-legend.position-bottom.apexcharts-align-left {
  -webkit-box-pack: center !important;
      -ms-flex-pack: center !important;
          justify-content: center !important; }

#yooChart1-4 {
  min-height: initial !important; }

.apexcharts-tooltip.apexcharts-theme-light {
  border-color: #e5e5e5 !important;
  border-radius: 10px; }

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background-color: #fff !important;
  border-color: #e5e5e5;
  font-size: 14px !important;
  padding: 5px 10px; }

.apexcharts-legend.position-bottom.apexcharts-align-left {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  bottom: 0px !important; }

.apexcharts-legend-marker {
  border-radius: 3px !important;
  margin-right: 4px !important; }

#yooChart4 {
  margin-top: -10px;
  margin-left: -10px;
  margin-right: 0px; }

.apexcharts-canvas .apexcharts-xaxistooltip {
  padding: 9px 10px;
  border-radius: 5px;
  background: #fff;
  border-color: #e5e5e5; }
.apexcharts-canvas .apexcharts-xaxistooltip-bottom:after {
  border-bottom-color: #fff; }
.apexcharts-canvas .apexcharts-xaxistooltip-bottom:before {
  border-bottom-color: #e5e5e5; }
.apexcharts-canvas .apexcharts-tooltip-marker {
  margin-right: 7px; }
.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active,
.apexcharts-canvas .apexcharts-tooltip-series-group:last-child {
  padding-bottom: 0px; }
.apexcharts-canvas .apexcharts-tooltip {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding-bottom: 5px; }

.yoo-performance-chart {
  margin-left: -20px;
  margin-right: -20px;
  margin-top: -16px;
  margin-bottom: -20px; }

/*========== Start Task List  ==========*/
.yoo-task-list.yoo-style1 .badge {
  line-height: 10px; }
.yoo-task-list.yoo-style1 > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative; }
  .yoo-task-list.yoo-style1 > li:not(:last-child) {
    margin-bottom: 15px; }
  .yoo-task-list.yoo-style1 > li:hover .yoo-button-group {
    opacity: 1; }
.yoo-task-list.yoo-style1 .yoo-task-checkmark {
  margin-right: 15px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-top: 1px; }
.yoo-task-list.yoo-style1 .yoo-task-icon {
  margin-right: 12px; }
  .yoo-task-list.yoo-style1 .yoo-task-icon img {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover; }
.yoo-task-list.yoo-style1 .yoo-task-text h3 {
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.2em;
  font-weight: 500; }
.yoo-task-list.yoo-style1 .yoo-button-group {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
.yoo-task-list.yoo-style1 .yoo-ativity-time {
  font-size: 12px;
  line-height: 22px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: -3px; }
  .yoo-task-list.yoo-style1 .yoo-ativity-time i,
  .yoo-task-list.yoo-style1 .yoo-ativity-time .hydrated {
    margin-right: 5px; }

@media screen and (max-width: 575px) {
  .yoo-cta.yoo-style2 {
    padding: 0;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start; }

  .yoo-padd-lr-30.yoo-card-content-height1 {
    padding-left: 15px;
    padding-right: 15px; } }
/*========== End Task List  ==========*/
.yoo-search.yoo-style1 {
  position: relative; }
  .yoo-search.yoo-style1 .yoo-search-input {
    width: 100%;
    border: 1px solid #e5e5e5;
    font-size: 14px;
    padding: 3px 15px;
    border-radius: 4px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    color: rgba(0, 0, 0, 0.7);
    padding-left: 32px; }
    .yoo-search.yoo-style1 .yoo-search-input:focus {
      outline: none; }
  .yoo-search.yoo-style1 .yoo-form-reset {
    position: absolute;
    height: 100%;
    width: 32px;
    top: 0;
    right: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 16px;
    border: 1px solid transparent;
    padding-bottom: 1px;
    color: rgba(0, 0, 0, 0.4);
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-search.yoo-style1 .yoo-form-reset:hover {
      color: rgba(0, 0, 0, 0.7); }
    .yoo-search.yoo-style1 .yoo-form-reset .yoo-reset-input {
      position: absolute;
      opacity: 0;
      z-index: 1;
      height: 30px;
      width: 32px;
      right: 0;
      top: 0; }
  .yoo-search.yoo-style1 .yoo-search-submit {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    border: none;
    background: transparent;
    position: absolute;
    top: 0;
    left: 0;
    width: 34px;
    height: 100%;
    color: rgba(0, 0, 0, 0.4);
    font-size: 20px;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-search.yoo-style1 .yoo-search-submit:hover {
      color: rgba(0, 0, 0, 0.7); }

.yoo-search.yoo-search-md .yoo-search-input {
  padding-top: 6px;
  padding-bottom: 6px; }

/*========== Start SVG Map  ==========*/
#svg-usa {
  height: 180px;
  background-color: transparent !important; }

.vmap-wrapper.yoo-style1,
.vmap-wrapper.yoo-style1#svg-usa {
  height: 280px;
  background-color: transparent !important; }

.jqvmap-label {
  padding: 2px 10px;
  background-color: #fff;
  border-radius: 2px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.7);
  opacity: 1;
  border: 1px solid #e5e5e5; }

.jqvmap-region {
  cursor: pointer;
  stroke-opacity: 1;
  stroke: #fff; }

.jqvmap-zoomin,
.jqvmap-zoomout {
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.4);
  width: 14px;
  height: 13px;
  line-height: 7px;
  padding: 0;
  left: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.jqvmap-zoomin:hover,
.jqvmap-zoomout:hover {
  background-color: rgba(0, 0, 0, 0.7); }

.jqvmap-zoomout {
  top: 28px; }

.yoo-svg-map-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-svg-map-wrap .yoo-svg-map.yoo-style1 {
    width: 60%; }
  .yoo-svg-map-wrap .yoo-progress-wrap-out {
    width: 40%; }
  .yoo-svg-map-wrap #svg-usa {
    height: 300px; }
  .yoo-svg-map-wrap .vmap-wrapper.yoo-style1,
  .yoo-svg-map-wrap .vmap-wrapper.yoo-style1#svg-usa {
    height: 300px; }

.vmap-wrapper.yoo-style1,
.vmap-wrapper.yoo-style1#svg-usa {
  height: 285px;
  background-color: transparent !important; }

@media screen and (max-width: 575px) {
  .yoo-svg-map-wrap {
    display: block; }
    .yoo-svg-map-wrap .yoo-svg-map.yoo-style1,
    .yoo-svg-map-wrap .yoo-progress-wrap-out {
      width: 100%; } }
/*========== End SVG Map  ==========*/
/*========== Start Pricing Table  ==========*/
.yoo-pricing-table.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 1px;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  margin: auto; }
  .yoo-pricing-table.yoo-style1 .yoo-pricing-col {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    border: 1px solid #e5e5e5;
    margin-left: -1px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end; }
  .yoo-pricing-table.yoo-style1 .yoo-pricing-row {
    text-align: center;
    min-height: 45px;
    border-bottom: 1px solid #e5e5e5;
    padding: 10px 15px;
    font-size: 14px; }
    .yoo-pricing-table.yoo-style1 .yoo-pricing-row:last-child {
      border-bottom: none; }
  .yoo-pricing-table.yoo-style1 .yoo-pricing-title {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    width: 280px; }
    .yoo-pricing-table.yoo-style1 .yoo-pricing-title .yoo-pricing-row {
      text-align: left; }
  .yoo-pricing-table.yoo-style1 .yoo-packeg-heading {
    background-color: rgba(0, 0, 0, 0.05); }
  .yoo-pricing-table.yoo-style1 .yoo-price {
    font-size: 48px;
    margin-bottom: 16px;
    margin-top: 6px; }
  .yoo-pricing-table.yoo-style1 .yoo-price-currency {
    font-style: initial;
    font-size: 16px;
    position: relative;
    top: -8px;
    margin-right: 3px; }
  .yoo-pricing-table.yoo-style1 .yoo-btn {
    margin-bottom: 4px; }

.yoo-packeg.yoo-style1 .yoo-packeg-heading,
.yoo-packeg.yoo-style2 .yoo-packeg-heading {
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 30px;
  font-size: 14px; }
.yoo-packeg.yoo-style1 .yoo-packeg-price,
.yoo-packeg.yoo-style2 .yoo-packeg-price {
  margin-bottom: 0;
  border-bottom: 1px solid #e5e5e5;
  font-size: 48px;
  font-weight: 500;
  padding: 23px 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
.yoo-packeg.yoo-style1 .yoo-packeg-currency,
.yoo-packeg.yoo-style2 .yoo-packeg-currency {
  font-style: normal;
  font-size: 16px;
  position: relative;
  top: 11px;
  margin-right: 2px; }
.yoo-packeg.yoo-style1 .yoo-packeg-per,
.yoo-packeg.yoo-style2 .yoo-packeg-per {
  padding-bottom: 7px;
  margin-left: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end; }
.yoo-packeg.yoo-style1 .yoo-packeg-btn,
.yoo-packeg.yoo-style2 .yoo-packeg-btn {
  display: block;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.6em;
  border-top: 1px solid #e5e5e5;
  color: rgba(0, 0, 0, 0.95);
  padding: 0.4em 1.5em;
  text-align: center; }
.yoo-packeg.yoo-style1 .yoo-packeg-btn:hover,
.yoo-packeg.yoo-style2 .yoo-packeg-btn:hover {
  background-color: rgba(0, 0, 0, 0.05); }
.yoo-packeg.yoo-style1.yoo-featured-packeg,
.yoo-packeg.yoo-style2.yoo-featured-packeg {
  margin-top: -25px; }
  .yoo-packeg.yoo-style1.yoo-featured-packeg .yoo-packeg-price,
  .yoo-packeg.yoo-style2.yoo-featured-packeg .yoo-packeg-price {
    padding: 36px 30px 35px; }
  .yoo-packeg.yoo-style1.yoo-featured-packeg .yoo-packeg-btn,
  .yoo-packeg.yoo-style2.yoo-featured-packeg .yoo-packeg-btn {
    color: rgba(255, 255, 255, 0.9);
    background-color: #34c759;
    border-color: #34c759; }
    .yoo-packeg.yoo-style1.yoo-featured-packeg .yoo-packeg-btn:hover,
    .yoo-packeg.yoo-style2.yoo-featured-packeg .yoo-packeg-btn:hover {
      opacity: 0.8; }

.yoo-packeg.yoo-style1,
.yoo-packeg.yoo-style2,
.yoo-packeg.yoo-style3 {
  border: 1px solid #e5e5e5;
  text-align: center; }

.yoo-packeg-feature {
  font-size: 14px;
  padding: 25px 30px; }
  .yoo-packeg-feature li:not(:last-child) {
    margin-bottom: 12px; }
  .yoo-packeg-feature .yoo-packeg-icon {
    margin-right: 8px;
    font-size: 12px; }

.yoo-packeg-per {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4); }

.yoo-packeg.yoo-style2 {
  text-align: left; }
  .yoo-packeg.yoo-style2 .yoo-packeg-price {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start; }

.yoo-packeg.yoo-style3 {
  padding: 15px; }
  .yoo-packeg.yoo-style3 .yoo-packeg-img {
    height: 160px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }
  .yoo-packeg.yoo-style3 .yoo-packeg-heading {
    font-size: 16px;
    line-height: 1.2em; }
  .yoo-packeg.yoo-style3 .yoo-packeg-price {
    font-size: 36px;
    margin-bottom: 0; }

.yoo-check {
  color: #34c759;
  font-size: 34px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
  .yoo-check i,
  .yoo-check i:before {
    line-height: inherit; }

.yoo-cross {
  color: rgba(0, 0, 0, 0.4);
  font-size: 28px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
  .yoo-cross i,
  .yoo-cross i:before {
    line-height: inherit; }

.yoo-pricing-tab.yoo-style1 .yoo-pricing-switch {
  margin-right: -30px;
  margin-left: -30px; }
.yoo-pricing-tab.yoo-style1 li {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 32px; }
  .yoo-pricing-tab.yoo-style1 li a {
    font-size: 15px;
    padding: 0 40px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }
  .yoo-pricing-tab.yoo-style1 li:first-child a {
    padding-left: 0; }
  .yoo-pricing-tab.yoo-style1 li:last-child a {
    padding-right: 0; }
.yoo-pricing-tab.yoo-style1 li.yoo-active .yoo-pricing-switch span {
  left: 3px; }

.yoo-pricing-switch {
  position: relative;
  height: 32px;
  width: 60px;
  background-color: #5ac8fa;
  border-radius: 16px;
  pointer-events: none;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }
  .yoo-pricing-switch span {
    display: block;
    height: 26px;
    width: 26px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    -webkit-box-shadow: -0.382px 1.963px 3.92px 0.08px rgba(0, 0, 0, 0.2);
            box-shadow: -0.382px 1.963px 3.92px 0.08px rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 31px;
    top: 3px;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }

.yoo-pricing-tab.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
  .yoo-pricing-tab.yoo-style2 .yoo-tab-links {
    border: 2px solid rgba(0, 0, 0, 0.4);
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    border-radius: 5px; }
  .yoo-pricing-tab.yoo-style2 a {
    min-width: 100px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 7px 10px;
    color: rgba(0, 0, 0, 0.4); }
    .yoo-pricing-tab.yoo-style2 a:hover {
      background-color: rgba(0, 0, 0, 0.05); }
  .yoo-pricing-tab.yoo-style2 .yoo-active a {
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff; }
  .yoo-pricing-tab.yoo-style2 li:first-child a {
    border-radius: 3px 0 0 3px; }
  .yoo-pricing-tab.yoo-style2 li:last-child a {
    border-radius: 0 3px 3px 0; }

.yoo-packeg.yoo-style3 .yoo-packeg-feature {
  padding-left: 0;
  padding-right: 0; }

.nav-tabs.yoo-switch-tab {
  background: transparent;
  padding: 0; }
  .nav-tabs.yoo-switch-tab .nav-item .nav-link:after,
  .nav-tabs.yoo-switch-tab .nav-item .nav-link:before {
    display: none; }
  .nav-tabs.yoo-switch-tab .yoo-switch {
    position: absolute;
    z-index: 3;
    width: 60px;
    height: 32px;
    right: -30px;
    top: 2px;
    pointer-events: none; }
    .nav-tabs.yoo-switch-tab .yoo-switch .yoo-switch-in {
      height: 26px;
      width: 26px;
      left: 31px;
      top: 3px; }
  .nav-tabs.yoo-switch-tab .nav-link.active + .yoo-switch .yoo-switch-in {
    left: 3px; }
  .nav-tabs.yoo-switch-tab .nav-item {
    margin: 0 !important; }
    .nav-tabs.yoo-switch-tab .nav-item:first-child .nav-link {
      padding-left: 10px;
      padding-right: 40px; }
    .nav-tabs.yoo-switch-tab .nav-item:last-child .nav-link {
      padding-left: 40px;
      padding-right: 10px; }
  .nav-tabs.yoo-switch-tab .nav-item .nav-link.active {
    -webkit-box-shadow: none;
            box-shadow: none; }

.yoo-pricing-table .yoo-check {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 18px;
  position: relative;
  top: 4px; }
.yoo-pricing-table .yoo-cross {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 18px;
  position: relative;
  top: 4px; }

@media screen and (max-width: 991px) {
  .yoo-pricing-table.yoo-style1 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }

  .yoo-pricing-table.yoo-style1 .yoo-pricing-col {
    width: 100%;
    margin-bottom: 30px; }
    .yoo-pricing-table.yoo-style1 .yoo-pricing-col:last-child {
      margin-bottom: 0; }

  .yoo-pricing-table.yoo-style3 .row > div {
    padding-left: 0px;
    padding-right: 0px; } }
/*========== End Pricing Table  ==========*/
/*========== Start Email  ==========*/
.yoo-outline-label {
  font-size: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  color: rgba(0, 0, 0, 0.4); }

.yoo-email-badgh {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  background-color: #34c759;
  border-radius: 4px;
  padding: 2px 6px; }

.yoo-email-text.yoo-style1 {
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-width: calc(100% - 50px); }
  .yoo-email-text.yoo-style1 .yoo-email-title {
    font-weight: 400;
    font-size: inherit;
    margin-right: 5px;
    margin-bottom: 0;
    line-height: inherit;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }

.yoo-table.yoo-style4 {
  padding-bottom: 30px;
  margin-bottom: -30px;
  padding-top: 30px;
  margin-top: -30px; }
  .yoo-table.yoo-style4 .table {
    border-bottom: 1px solid #e5e5e5; }
  .yoo-table.yoo-style4 .table td {
    vertical-align: middle;
    border-color: #e5e5e5; }
  .yoo-table.yoo-style4 td {
    padding: 8px 7px; }
    .yoo-table.yoo-style4 td:first-child {
      padding-left: 30px;
      width: 56px; }
    .yoo-table.yoo-style4 td:nth-child(2) {
      width: 36px; }
    .yoo-table.yoo-style4 td:nth-child(3) {
      width: 37px; }
    .yoo-table.yoo-style4 td:nth-child(4) {
      width: 185px; }
    .yoo-table.yoo-style4 td:nth-child(5) {
      width: 60px; }
    .yoo-table.yoo-style4 td:last-child {
      padding-right: 30px;
      min-width: 110px; }
  .yoo-table.yoo-style4 tr {
    position: relative;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer; }
    .yoo-table.yoo-style4 tr:hover {
      -webkit-box-shadow: 0px 2px 9px 1px rgba(0, 0, 0, 0.1);
              box-shadow: 0px 2px 9px 1px rgba(0, 0, 0, 0.1);
      background-color: #fff; }

.yoo-email-sent-time {
  font-size: 16px;
  text-align: right; }

.yoo-new-message {
  background-color: rgba(0, 0, 0, 0.05); }
  .yoo-new-message .yoo-user.yoo-style2 .yoo-user-name,
  .yoo-new-message .yoo-email-text.yoo-style1 .yoo-email-title,
  .yoo-new-message .yoo-email-sent-time {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.95); }

.yoo-email-header.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  height: 80px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 30px; }

.yoo-icon-group-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-icon-group.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 15px; }
  .yoo-icon-group.yoo-style1:not(:last-child) {
    margin-right: 23px;
    padding-right: 23px;
    position: relative; }
    .yoo-icon-group.yoo-style1:not(:last-child):before {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      right: 0;
      background: #e5e5e5;
      top: 50%;
      margin-top: -10px; }
  .yoo-icon-group.yoo-style1 .yoo-search.yoo-search-md .yoo-search-input {
    padding-top: 3px;
    padding-bottom: 3px; }
  .yoo-icon-group.yoo-style1 .yoo-search.yoo-style1 .yoo-search-submit {
    font-size: 16px; }
  .yoo-icon-group.yoo-style1 .yoo-search.yoo-style1 {
    width: 300px; }
  .yoo-icon-group.yoo-style1 .yoo-search-wrap {
    margin-left: 10px;
    padding-left: 25px;
    position: relative; }
    .yoo-icon-group.yoo-style1 .yoo-search-wrap:before {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      left: 0;
      background: #e5e5e5;
      top: 50%;
      margin-top: -10px; }

.yoo-email-header-mars {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-email-header-mars .yoo-task-checkmark {
    margin-right: 5px; }

.yoo-email-header-marks-btn,
.yoo-icon-group-icon,
.yoo-email-header-more-list-btn,
.yoo-email-header-setting-btn {
  font-size: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: #b5b5b5;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer; }
  .yoo-email-header-marks-btn:hover,
  .yoo-icon-group-icon:hover,
  .yoo-email-header-more-list-btn:hover,
  .yoo-email-header-setting-btn:hover {
    color: rgba(0, 0, 0, 0.7); }

.yoo-email-header-marks-btn,
.yoo-email-header-more-list-btn {
  border: none;
  background: transparent;
  padding: 0;
  margin: 0; }
  .yoo-email-header-marks-btn:after,
  .yoo-email-header-more-list-btn:after {
    display: none; }
  .yoo-email-header-marks-btn:focus,
  .yoo-email-header-more-list-btn:focus {
    outline: none; }

.yoo-email-header-right-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-email-header-right-list li:not(:first-child) {
    margin-left: 20px; }

.yoo-email-pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 13px; }
  .yoo-email-pagination .yoo-email-page-number {
    margin-right: 10px; }
  .yoo-email-pagination .yoo-navigation.yoo-style1 {
    color: rgba(0, 0, 0, 0.4); }
    .yoo-email-pagination .yoo-navigation.yoo-style1 a:hover {
      color: rgba(0, 0, 0, 0.7); }

.yoo-email-details-body {
  padding: 30px;
  border-top: 1px solid #e5e5e5; }
  .yoo-email-details-body .yoo-email-user-email {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }

.yoo-email-details-meta-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .yoo-email-details-meta-right .yoo-user-img {
    height: 32px;
    width: 32px; }
  .yoo-email-details-meta-right .yoo-email-details-time {
    padding-right: 25px;
    margin-right: 23px;
    position: relative; }
    .yoo-email-details-meta-right .yoo-email-details-time:after {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      right: 0;
      background: #e5e5e5;
      top: 50%;
      margin-top: -10px; }

.yoo-email-details-title {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-bottom: 9px; }
  .yoo-email-details-title .yoo-email-details-title-badgh {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.7);
    background-color: #e5e5e5;
    border-radius: 4px;
    padding: 4px 6px;
    margin-left: 10px;
    margin-top: 1px;
    line-height: 1.3em; }

.yoo-email-details-meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 20px; }

.yoo-email-user-email-toggle-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-email-user-email-toggle-btn:hover {
    color: rgba(0, 0, 0, 0.7); }

.yoo-email-details-text {
  font-size: 16px;
  line-height: 1.6em;
  max-width: 780px;
  padding-top: 25px;
  padding-bottom: 25px;
  color: rgba(0, 0, 0, 0.95); }

#yoo-email-editor {
  height: 125px;
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  text-shadow: none;
  margin-top: 30px;
  padding: 20px 25px; }
  #yoo-email-editor .ql-editor.ql-blank::before {
    left: 25px;
    right: 25px;
    font-style: normal;
    color: rgba(0, 0, 0, 0.4);
    font-size: 16px;
    line-height: 1.6em;
    top: 20px; }
  #yoo-email-editor .ql-editor {
    line-height: 1.6em;
    padding: 0;
    font-size: 16px;
    line-height: 1.6em; }
  #yoo-email-editor strong {
    font-weight: 600; }

.ql-container {
  font-family: inherit; }

.yoo-email-btn-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 15px; }

.yoo-email-editor-toolbox-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.yoo-email-editor-btn .yoo-btn {
  min-width: 100px; }

#yoo-email-editor-toolbox {
  border: none;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  #yoo-email-editor-toolbox button {
    font-weight: 500;
    font-size: 21px;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: initial;
    color: rgba(0, 0, 0, 0.4);
    margin-right: 10px;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    #yoo-email-editor-toolbox button.ql-active, #yoo-email-editor-toolbox button:hover {
      color: rgba(0, 0, 0, 0.7); }

.yoo-email-editor-file-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-email-editor-file-group button {
    padding: 0;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 21px;
    color: rgba(0, 0, 0, 0.4);
    margin-right: 6px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
    .yoo-email-editor-file-group button:hover {
      color: rgba(0, 0, 0, 0.7); }
    .yoo-email-editor-file-group button:focus {
      outline: none; }

.yoo-email-user-wrap {
  height: 81px;
  width: 100%;
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #e5e5e5; }
  .yoo-email-user-wrap .yoo-user.yoo-style2 .yoo-user-img {
    height: 36px;
    width: 36px; }

@media screen and (max-width: 1199px) {
  .yoo-email-text.yoo-style1 .yoo-email-body {
    display: none; }

  .yoo-email-header.yoo-style1 {
    padding-left: 90px; }

  .yoo-email-header.yoo-style1 .yoo-mobile-hide {
    display: none; } }
@media screen and (max-width: 991px) {
  .yoo-table.yoo-style4 td:nth-child(6) {
    display: none; }

  .yoo-icon-group.yoo-style1:not(:last-child) {
    margin-right: 13px;
    padding-right: 13px; }

  .yoo-email-details-meta-right .yoo-email-details-time {
    padding-right: 15px;
    margin-right: 13px; } }
@media screen and (max-width: 575px) {
  .yoo-icon-group.yoo-style1 .yoo-search.yoo-style1 {
    width: 100%; }

  .yoo-icon-group.yoo-style1 .yoo-search-wrap {
    margin-left: 0;
    padding-left: 0; }

  .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 10px; }

  .yoo-icon-group.yoo-style1 li {
    margin-right: 10px; }

  .yoo-icon-group.yoo-style1 li:last-child {
    margin-right: 0px; }

  .yoo-email-header-right-list li:not(:first-child) {
    margin-left: 15px; }

  .yoo-table.yoo-style4 td {
    padding: 8px 5px; }
  .yoo-table.yoo-style4 td:first-child {
    padding-left: 20px;
    width: 46px; }
  .yoo-table.yoo-style4 td:last-child {
    padding-right: 20px;
    min-width: 95px; }
  .yoo-table.yoo-style4 td:nth-child(5),
  .yoo-table.yoo-style4 .yoo-user.yoo-style2 .yoo-user-img {
    display: none; }

  .yoo-email-header.yoo-style1 .yoo-icon-group.yoo-style1:not(:last-child) {
    margin-right: 0;
    padding-right: 0; }

  .yoo-email-header.yoo-style1 .yoo-icon-group.yoo-style1:not(:last-child):before {
    display: none; }

  .yoo-email-details-title {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse; }

  .yoo-email-details-title .yoo-email-details-title-badgh {
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 6px; }

  .yoo-email-details-meta-right .yoo-email-details-time {
    margin-top: 5px;
    margin-bottom: 5px; }

  .yoo-email-header.yoo-style1 {
    padding-left: 80px;
    padding-right: 20px; } }
/*========== End Email  ==========*/
/*========== Start User  ==========*/
.yoo-user-heading.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 8px 30px 7px;
  margin-top: 0px; }
  .yoo-user-heading.yoo-style1 .yoo-user-title {
    font-size: 12px;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4);
    font-weight: 500;
    margin: 0; }
  .yoo-user-heading.yoo-style1 .yoo-user-options {
    font-size: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    color: rgba(0, 0, 0, 0.4);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer; }
    .yoo-user-heading.yoo-style1 .yoo-user-options:hover {
      color: rgba(0, 0, 0, 0.7); }
    .yoo-user-heading.yoo-style1 .yoo-user-options .yoo-user-options-btn {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex; }
  .yoo-user-heading.yoo-style1.yoo-type1 {
    padding-left: 0;
    padding-right: 0; }

.yoo-user-list .yoo-user.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 10px 20px; }
.yoo-user-list .yoo-user.yoo-style1:hover,
.yoo-user-list .active .yoo-user.yoo-style1 {
  background-color: rgba(0, 0, 0, 0.05); }
.yoo-user-list .yoo-user-img {
  position: relative;
  border-radius: 50%;
  margin-right: 8px;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }
  .yoo-user-list .yoo-user-img img {
    height: 34px;
    width: 34px;
    -o-object-fit: cover;
       object-fit: cover;
    border-radius: 50%; }
.yoo-user-list .yoo-user-name {
  font-size: 14px;
  margin-bottom: 0px;
  font-weight: 400; }
.yoo-user-list .yoo-user-time {
  display: block;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 1.2; }
.yoo-user-list .yoo-online-status {
  height: 8px;
  width: 8px;
  position: absolute;
  right: 0;
  bottom: 0;
  -webkit-box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9); }
.yoo-user-list .yoo-group-member {
  font-size: 12px;
  line-height: 1.3em; }

.yoo-user.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-user.yoo-style2 .yoo-user-img {
    border-radius: 50%;
    height: 34px;
    width: 34px;
    overflow: hidden;
    margin-right: 10px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
    .yoo-user.yoo-style2 .yoo-user-img img {
      height: 100%;
      width: 100%; }
  .yoo-user.yoo-style2 .yoo-user-name {
    font-weight: 400;
    font-size: 16px;
    margin-bottom: 0;
    padding-top: 2px; }
  .yoo-user.yoo-style2 .yoo-online-status {
    height: 8px;
    width: 8px; }

.yoo-user.yoo-style3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-user.yoo-style3 .yoo-user-img {
    border-radius: 50%;
    height: 36px;
    width: 36px;
    overflow: hidden;
    margin-right: 10px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
    .yoo-user.yoo-style3 .yoo-user-img img {
      height: 100%;
      width: 100%; }
  .yoo-user.yoo-style3 .yoo-user-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 3px;
    padding-top: 8px; }
  .yoo-user.yoo-style3 .yoo-online-status {
    height: 8px;
    width: 8px; }
  .yoo-user.yoo-style3 .yoo-post-label.yoo-style1 {
    margin-bottom: -2px; }

.yoo-user.yoo-style4 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-user.yoo-style4 .yoo-user-img {
    margin-right: 20px;
    height: 128px;
    width: 128px;
    border-radius: 50%;
    overflow: hidden; }
  .yoo-user.yoo-style4 .yoo-user-name {
    font-size: 20px;
    margin-bottom: 0; }
  .yoo-user.yoo-style4 .yoo-lavel {
    font-size: 14px;
    line-height: 1.6em; }
  .yoo-user.yoo-style4 .yoo-user-btns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
    .yoo-user.yoo-style4 .yoo-user-btns .btn {
      border-radius: 4px; }
      .yoo-user.yoo-style4 .yoo-user-btns .btn:focus {
        outline: none; }
  .yoo-user.yoo-style4 .btn {
    min-width: 140px;
    padding: 8px 15px;
    font-size: 14px;
    margin-right: 10px; }

.yoo-users.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 5px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-users.yoo-style1 li {
    border-radius: 50%;
    overflow: hidden;
    background-color: #fff;
    height: 30px;
    width: 30px;
    margin-left: -5px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    font-size: 11px;
    font-weight: 500;
    position: relative; }
    .yoo-users.yoo-style1 li a {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      height: 100%;
      width: 100%;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #fff; }

/*========== End User  ==========*/
/*========== Start Calendar  ==========*/
#yoo-calendar a {
  -webkit-transition: none;
  transition: none; }
#yoo-calendar .fc-event,
#yoo-calendar .fc-event-dot {
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 0;
  border: none;
  color: #34c759;
  margin: 0;
  padding: 11px 30px;
  border-left: 3px solid #34c759; }
#yoo-calendar .fc-time {
  margin-top: 3px;
  font-weight: 400;
  font-size: 12px;
  text-transform: uppercase; }
#yoo-calendar .fc-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse; }
#yoo-calendar .fc-day-grid-event .fc-time:before {
  content: "at";
  text-transform: lowercase;
  margin-right: 2px; }
#yoo-calendar .fc-day-grid-event .fc-time:after {
  content: "m"; }
#yoo-calendar .fc-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0px; }
#yoo-calendar.fc-unthemed .fc-content,
#yoo-calendar.fc-unthemed .fc-divider,
#yoo-calendar.fc-unthemed .fc-list-heading td,
#yoo-calendar.fc-unthemed .fc-list-view,
#yoo-calendar.fc-unthemed .fc-popover,
#yoo-calendar.fc-unthemed .fc-row,
#yoo-calendar.fc-unthemed tbody,
#yoo-calendar.fc-unthemed td,
#yoo-calendar.fc-unthemed th,
#yoo-calendar.fc-unthemed thead {
  border-color: #e5e5e5; }
#yoo-calendar .fc-day-top .fc-day-number {
  float: initial;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  display: inline-block;
  padding: 15px 30px; }
#yoo-calendar .fc-day-header span,
#yoo-calendar .fc-day-header a {
  display: block;
  padding: 16px 15px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4); }
#yoo-calendar .fc-center h2 {
  font-size: 16px; }
#yoo-calendar .fc-toolbar.fc-header-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 17px 0;
  position: relative;
  margin-bottom: 0;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5; }
#yoo-calendar .fc-center h2 {
  font-size: 16px; }
#yoo-calendar .fc-toolbar .fc-left {
  float: left;
  position: absolute;
  right: 20px;
  width: 126px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  top: 11px;
  height: 30px; }
  #yoo-calendar .fc-toolbar .fc-left .fc-button-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    position: relative; }
    #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-button {
      position: absolute;
      top: 0;
      padding: 0;
      width: 28px;
      background: transparent;
      border: none;
      height: 28px;
      line-height: 28px;
      -webkit-box-shadow: none;
              box-shadow: none;
      -webkit-transition: all 0.3s ease;
      transition: all 0.3s ease; }
      #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-button .fc-icon {
        font-size: 10px;
        color: rgba(0, 0, 0, 0.4);
        top: -2px; }
      #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-button:hover {
        background-color: rgba(0, 0, 0, 0.05); }
      #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-button:focus {
        outline: none; }
    #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-prev-button {
      left: 0; }
    #yoo-calendar .fc-toolbar .fc-left .fc-button-group .fc-next-button {
      right: 0; }
  #yoo-calendar .fc-toolbar .fc-left .fc-today-button {
    position: absolute;
    left: 28px;
    margin-left: 0;
    width: 68px;
    border: none;
    border-left: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    border-radius: 0;
    background: transparent;
    color: rgba(0, 0, 0, 0.7);
    font-weight: 400;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
    -webkit-box-shadow: none;
            box-shadow: none; }
    #yoo-calendar .fc-toolbar .fc-left .fc-today-button:hover {
      background-color: rgba(0, 0, 0, 0.05); }
#yoo-calendar .fc-toolbar .fc-right {
  position: absolute;
  left: 20px;
  top: 11px; }
  #yoo-calendar .fc-toolbar .fc-right .fc-button-group {
    background-color: #f0f0f7;
    border-radius: 5px;
    padding: 3px;
    border: none; }
    #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button {
      background: transparent;
      border: none;
      line-height: 1.6em;
      padding: 0;
      height: 28px;
      width: 70px;
      border: none;
      margin: 0;
      font-size: 12px;
      text-transform: capitalize;
      border-right: none;
      -webkit-box-shadow: none;
              box-shadow: none;
      color: rgba(0, 0, 0, 0.95);
      text-shadow: none;
      font-size: 12px;
      font-weight: 500;
      font-family: inherit;
      border-radius: 5px;
      position: relative; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:before, #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:after {
        content: '';
        position: absolute;
        height: 20px;
        width: 1px;
        background-color: rgba(0, 0, 0, 0.1);
        top: 50%;
        -webkit-transform: translateY(-50%);
                transform: translateY(-50%); }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:before {
        right: 0; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:after {
        left: -1px; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:first-child:after {
        display: none; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:last-child:before {
        display: none; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button.fc-state-active {
        background-color: #fff;
        -webkit-box-shadow: 0px 2px 11.52px 0.48px rgba(0, 0, 0, 0.1);
                box-shadow: 0px 2px 11.52px 0.48px rgba(0, 0, 0, 0.1); }
        #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button.fc-state-active:before, #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button.fc-state-active:after {
          background-color: #f0f0f7; }
      #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button:focus {
        outline: none; }
#yoo-calendar .fc-more {
  margin: 0;
  font-size: 14px;
  color: #fff;
  padding: 11px 30px;
  display: block;
  background-color: rgba(0, 0, 0, 0.4); }
#yoo-calendar.fc-unthemed .fc-popover {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  overflow: hidden;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  background-color: #fff; }
#yoo-calendar.fc-unthemed .fc-popover .fc-header {
  background: rgba(0, 0, 0, 0.05);
  padding: 12px 15px;
  border-bottom: 1px solid #e5e5e5; }
#yoo-calendar .fc-header.fc-widget-header .fc-title {
  font-size: 14px;
  margin: 0;
  font-weight: 400; }
#yoo-calendar .fc-more-popover .fc-event-container {
  padding: 0; }
#yoo-calendar .fc-row.fc-rigid .fc-content-skeleton tbody tr:nth-child(even) .fc-event-container .fc-event,
#yoo-calendar .fc-event-container .fc-event:nth-child(even) {
  background-color: rgba(0, 122, 255, 0.1);
  border-left: 3px solid #007aff;
  color: #007aff; }

#yoo-calendar.fc-unthemed .fc-content-skeleton td {
  border: none; }

.fc-highlight {
  background: rgba(0, 0, 0, 0.05);
  opacity: 1; }

.fc-highlight-skeleton td {
  border: none !important; }

.fc-unthemed td.fc-today {
  background-color: rgba(0, 0, 0, 0.05); }

#yoo-calendar.fc-unthemed .fc-popover.fc-more-popover {
  width: 225px; }

.yoo-card.yoo-style1.yoo-calendar-card {
  overflow: hidden; }
  .yoo-card.yoo-style1.yoo-calendar-card .yoo-card-heading {
    border: 1px solid #e5e5e5;
    border-radius: 4px 4px 0 0;
    margin-top: -1px;
    margin-left: -1px;
    margin-right: -1px; }
  .yoo-card.yoo-style1.yoo-calendar-card #yoo-calendar {
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: -1px; }

.yoo-btn.yoo-add-btn {
  background-color: #007aff;
  border-radius: 4px;
  color: #fff;
  font-size: 13px;
  font-weight: 500;
  padding: 3px 18px;
  line-height: 1.6em; }
  .yoo-btn.yoo-add-btn:hover {
    opacity: 0.8; }
  .yoo-btn.yoo-add-btn .hydrated {
    font-size: 24px;
    margin-right: 5px;
    margin-left: -5px; }

@media screen and (max-width: 767px) {
  #yoo-calendar .fc-toolbar.fc-header-toolbar {
    padding: 35px 0; }

  #yoo-calendar .fc-toolbar .fc-left {
    top: 31px; }

  #yoo-calendar .fc-toolbar .fc-center {
    display: inline-block;
    position: absolute;
    right: 20px;
    top: 6px; }

  #yoo-calendar .fc-toolbar .fc-right {
    top: 27px; }

  #yoo-calendar .fc-toolbar .fc-right .fc-button-group .fc-button {
    width: 55px; } }
/*========== End Calendar  ==========*/
/*========== Start Contact Page  ==========*/
.yoo-contact-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-contact-wrap.yoo-style1 .yoo-contact-left {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    border-right: 1px solid #e5e5e5; }
  .yoo-contact-wrap.yoo-style1 .yoo-contact-right {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }

.yoo-toolbtn {
  height: 41px;
  width: 46px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border: 2px solid #e5e5e5;
  border-radius: 4px;
  background-color: transparent;
  padding: 5px;
  font-size: 22px;
  color: rgba(0, 0, 0, 0.7);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-toolbtn:hover {
    background-color: rgba(0, 0, 0, 0.05); }
  .yoo-toolbtn:after {
    display: none; }

.yoo-contact-info-list {
  font-size: 14px;
  line-height: 1.6em; }
  .yoo-contact-info-list li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 2px; }
    .yoo-contact-info-list li:last-child {
      margin-bottom: 0; }
  .yoo-contact-info-list .yoo-contact-info-label {
    color: rgba(0, 0, 0, 0.4);
    width: 200px; }

@media screen and (max-width: 991px) {
  .yoo-contact-wrap.yoo-style1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    display: block; }

  .yoo-contact-wrap.yoo-style1 .yoo-contact-left {
    border-right: none;
    border-bottom: 1px solid #e5e5e5; } }
/*========== End Contact Page  ==========*/
/*========== Start Attachment List ==========*/
.yoo-attachment-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding-bottom: 15px; }
  .yoo-attachment-list.yoo-style1 {
    display: block; }
  .yoo-attachment-list li {
    margin-right: 15px;
    margin-bottom: 15px; }

.yoo-attachment {
  -webkit-box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  padding: 10px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-right: 40px;
  width: 100%;
  max-width: 260px; }
  .yoo-attachment .yoo-attachment-img {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    height: 34px;
    width: 34px;
    margin-right: 8px; }
    .yoo-attachment .yoo-attachment-img img {
      height: 100%;
      width: 100%;
      -o-object-fit: cover;
         object-fit: cover; }
  .yoo-attachment .yoo-attachment-file {
    font-size: 40px;
    margin-right: 8px;
    margin-top: -3px;
    margin-bottom: -3px;
    color: rgba(0, 0, 0, 0.4);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .yoo-attachment .yoo-attachment-info {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }
  .yoo-attachment .yoo-attachment-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 1px; }
  .yoo-attachment .yoo-attachment-size {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 1.2em; }
  .yoo-attachment .yoo-attachment-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }
    .yoo-attachment .yoo-attachment-toggle .yoo-attachment-toggle-btn {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      font-size: 22px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.4);
      cursor: pointer;
      -webkit-transition: all 0.3s ease;
      transition: all 0.3s ease; }
      .yoo-attachment .yoo-attachment-toggle .yoo-attachment-toggle-btn:hover {
        color: rgba(0, 0, 0, 0.7); }

/*========== End Attachment List ==========*/
/*========== Start Error Page  ==========*/
.yoo-error-wrap.yoo-style1 {
  padding: 60px 0; }

.yoo-error-wrap.yoo-style1,
.yoo-error-wrap.yoo-style2 {
  min-height: 100vh;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-error-wrap.yoo-style1 .container,
  .yoo-error-wrap.yoo-style2 .container {
    max-width: 1330px; }

.yoo-error-text.yoo-style1 .yoo-error-title {
  font-size: 48px;
  margin-bottom: 15px; }
.yoo-error-text.yoo-style1 .yoo-error-desc {
  font-size: 16px;
  line-height: 1.6em;
  margin-bottom: 25px;
  max-width: 560px; }
.yoo-error-text.yoo-style1 .yoo-style1.yoo-color3 {
  color: #007aff;
  border-color: #007aff;
  background-color: transparent; }
  .yoo-error-text.yoo-style1 .yoo-style1.yoo-color3:hover {
    color: #fff;
    background-color: #007aff;
    border-color: #007aff; }
.yoo-error-text.yoo-style1.yoo-color1 .yoo-error-title {
  color: #fff; }
.yoo-error-text.yoo-style1.yoo-color1 .yoo-error-desc {
  color: #fff;
  opacity: 0.7; }
.yoo-error-text.yoo-style1.yoo-color1 .yoo-style1.yoo-color3 {
  color: #fff;
  border-color: #fff;
  background-color: transparent; }
  .yoo-error-text.yoo-style1.yoo-color1 .yoo-style1.yoo-color3:hover {
    color: #8e8e93; }

.yoo-error-img.yoo-style2 {
  width: 50vw;
  height: 100vh;
  margin-left: -15px; }

.yoo-error-wrap.text-center .yoo-error-desc {
  margin-left: auto;
  margin-right: auto; }

@media screen and (max-width: 991px) {
  .yoo-error-img.yoo-style2 {
    width: 100%;
    height: 350px;
    margin-left: -0; }

  .yoo-error-wrap.yoo-style2 {
    padding: 60px 0; } }
/*========== End Error Page  ==========*/
.yoo-form.yoo-style1 .yoo-form-title,
.yoo-form.yoo-style2 .yoo-form-title {
  font-size: 32px;
  margin-bottom: 0; }
.yoo-form.yoo-style1 .yoo-form-subtitle,
.yoo-form.yoo-style2 .yoo-form-subtitle {
  font-size: 16px;
  line-height: 1.6em; }
.yoo-form.yoo-style1 .row,
.yoo-form.yoo-style2 .row {
  margin-right: -7px;
  margin-left: -7px; }
  .yoo-form.yoo-style1 .row [class*=col-],
  .yoo-form.yoo-style2 .row [class*=col-] {
    padding-left: 7px;
    padding-right: 7px; }

.yoo-form-separator {
  overflow: hidden;
  display: block;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
  position: relative; }
  .yoo-form-separator:before, .yoo-form-separator:after {
    content: '';
    position: absolute;
    height: 1px;
    width: 50%;
    background-color: #e5e5e5;
    top: 9px; }
  .yoo-form-separator:before {
    left: -16px; }
  .yoo-form-separator:after {
    right: -16px; }

.yoo-form-btn.yoo-style1 {
  width: 100%;
  padding: 0px;
  height: 42px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6em;
  border-radius: 7px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center; }
  .yoo-form-btn.yoo-style1 i {
    width: 42px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 20px; }
  .yoo-form-btn.yoo-style1 span {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }
  .yoo-form-btn.yoo-style1:hover {
    opacity: 0.85; }

.yoo-form-btn.yoo-style1.yoo-color1 {
  background-color: #007aff;
  color: rgba(255, 255, 255, 0.9); }
.yoo-form-btn.yoo-style1.yoo-colo2 {
  background-color: #007aff;
  color: rgba(255, 255, 255, 0.9); }
.yoo-form-btn.yoo-style1.yoo-colo3 {
  background-color: #ff3b30;
  color: rgba(255, 255, 255, 0.9); }

.yoo-form-btn.yoo-style2.yoo-color1:before {
  background-color: #e5e5e5; }
.yoo-form-btn.yoo-style2.yoo-color1:after {
  background-color: rgba(0, 0, 0, 0.7); }

.yoo-form-btn.yoo-style2 {
  display: inline-block;
  position: relative;
  top: 2px;
  line-height: 1.35em;
  color: rgba(0, 0, 0, 0.95); }
  .yoo-form-btn.yoo-style2:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.4); }
  .yoo-form-btn.yoo-style2:after {
    content: "";
    width: 100%;
    height: 1px;
    position: absolute;
    bottom: 0;
    left: 0;
    -webkit-transform-origin: right center;
            transform-origin: right center;
    -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition: -webkit-transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    background-color: #007aff; }
  .yoo-form-btn.yoo-style2:hover {
    color: #007aff; }

.yoo-form-btn.yoo-style2:hover:after {
  opacity: 1;
  -webkit-transform-origin: left center;
          transform-origin: left center;
  -webkit-transform: scaleX(1);
          transform: scaleX(1); }

.yoo-form-btn.yoo-style2.yoo-type1:before,
.yoo-form-btn.yoo-style2.yoo-type1:after {
  height: 1px; }

.yoo-social-area.yoo-style1 li:not(:last-child) {
  margin-bottom: 10px; }

.yoo-login-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 100vh;
  padding: 60px 0; }

.yoo-signup-img.yoo-style1 {
  padding: 0 15px; }

.yoo-termas {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }
  .yoo-termas a {
    margin-left: 5px; }

.yoo-login-wrap.yoo-style1 .yoo-termas {
  margin-bottom: -3px;
  margin-top: -2px; }

.yoo-form.yoo-style2 {
  max-width: 500px;
  width: 100%;
  background-color: #fff;
  border-radius: 10px;
  margin: auto;
  padding: 50px;
  text-align: center; }

.yoo-login-wrap.yoo-style2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .yoo-login-wrap.yoo-style2 .yoo-left-login {
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none;
    max-width: 600px;
    min-height: 100vh;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    position: relative;
    padding-bottom: 60px; }
    .yoo-login-wrap.yoo-style2 .yoo-left-login .yoo-footer-nav {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 11px 0; }
  .yoo-login-wrap.yoo-style2 .yoo-left-right {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }

.yoo-login-wrap.yoo-style3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  min-height: 100vh;
  padding: 45px 0; }
  .yoo-login-wrap.yoo-style3 .yoo-left-login,
  .yoo-login-wrap.yoo-style3 .yoo-left-right {
    padding: 15px; }

.yoo-login-image {
  min-width: 460px;
  border-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  position: relative;
  overflow: hidden; }
  .yoo-login-image.yoo-height1 {
    height: 573px; }
  .yoo-login-image.yoo-height2 {
    height: 604px; }
  .yoo-login-image.yoo-height3 {
    height: 620px; }
  .yoo-login-image .yoo-login-image-text {
    color: #fff;
    padding: 50px;
    width: 100%;
    background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(0, 0, 0, 0.8)));
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); }
    .yoo-login-image .yoo-login-image-text p {
      font-size: 16px;
      line-height: 1.6em;
      margin-bottom: 9px; }
    .yoo-login-image .yoo-login-image-text span {
      display: block;
      font-size: 14px;
      opacity: 0.8; }

.yoo-forget-pass-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

@media screen and (max-width: 991px) {
  .yoo-login-wrap.yoo-style3 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; } }
@media screen and (max-width: 575px) {
  .yoo-login-image {
    min-width: 100%; }

  .yoo-login-image-text br {
    display: none; }

  .yoo-form-field-wrap.yoo-style1 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }

  .yoo-form-field-wrap.yoo-style1 .yoo-form-field-label {
    width: 100%;
    padding-right: 0;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    padding-left: 0; } }
table.dataTable tbody th,
table.dataTable tbody td {
  padding: 10px 20px; }

table.dataTable thead th,
table.dataTable thead td {
  padding: 10px 20px;
  border-bottom-color: #e5e5e5;
  border-top: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  background: rgba(0, 0, 0, 0.05);
  position: relative; }

table.dataTable.display tbody tr.odd > .sorting_1,
table.dataTable.order-column.stripe tbody tr.odd > .sorting_1,
table.dataTable.stripe tbody tr.odd,
table.dataTable.display tbody tr.odd,
table.dataTable.display tbody tr.even > .sorting_1,
table.dataTable.order-column.stripe tbody tr.even > .sorting_1 {
  background-color: transparent; }

table.dataTable.stripe tbody tr:hover,
table.dataTable.display tbody tr.odd:hover,
table.dataTable.hover tbody tr:hover,
table.dataTable.display tbody tr:hover {
  background-color: transparent !important; }

table.dataTable.display tbody td {
  border-right: 1px solid #e5e5e5; }

table.dataTable.row-border tbody th,
table.dataTable.row-border tbody td,
table.dataTable.display tbody th,
table.dataTable.display tbody td {
  border-color: #e5e5e5; }

table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting {
  background-image: initial; }

table.dataTable thead .sorting_asc .yoo-filter-btn {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg); }

table.dataTable thead th .yoo-filter-btn,
table.dataTable thead td .yoo-filter-btn {
  opacity: 0; }
table.dataTable thead th:hover .yoo-filter-btn,
table.dataTable thead td:hover .yoo-filter-btn {
  opacity: 1; }

table.dataTable.no-footer {
  border-bottom: none; }

table.dataTable tbody tr {
  background: transparent; }

.dataTables_wrapper .dataTables_length {
  display: none; }

.dataTables_wrapper .dataTables_filter {
  float: left;
  margin-left: 20px;
  margin-bottom: 15px; }
  .dataTables_wrapper .dataTables_filter label {
    margin-bottom: 0; }

.dataTables_wrapper .dataTables_filter input {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  height: 36px;
  width: 210px;
  padding: 5px 15px 5px 33px;
  font-size: 14px;
  margin-left: 0;
  background-image: url(../img/search.svg);
  background-repeat: no-repeat;
  background-position: 7px center; }
  .dataTables_wrapper .dataTables_filter input:focus {
    outline: none; }

.dataTables_wrapper .dataTables_info {
  float: right;
  clear: initial;
  padding-top: 8px;
  margin-right: 10px; }

.dataTables_wrapper .dataTables_paginate {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-right: 10px; }
  .dataTables_wrapper .dataTables_paginate > span {
    display: none; }
  .dataTables_wrapper .dataTables_paginate > .paginate_button {
    font-size: 18px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    height: 34px;
    width: 34px;
    margin: 0;
    border: none !important;
    padding: 0;
    color: rgba(0, 0, 0, 0.7) !important; }
    .dataTables_wrapper .dataTables_paginate > .paginate_button.disabled {
      color: rgba(0, 0, 0, 0.4) !important; }
      .dataTables_wrapper .dataTables_paginate > .paginate_button.disabled:hover {
        color: rgba(0, 0, 0, 0.4) !important; }
    .dataTables_wrapper .dataTables_paginate > .paginate_button.previous {
      border-right: 1px solid #e5e5e5 !important; }
    .dataTables_wrapper .dataTables_paginate > .paginate_button:hover {
      background: rgba(0, 0, 0, 0.05);
      color: rgba(0, 0, 0, 0.7) !important; }

.yooDataTableWrap {
  position: relative; }
  .yooDataTableWrap .dataTables_wrapper {
    overflow-x: auto;
    overflow-y: hidden; }
  .yooDataTableWrap table {
    min-width: 1100px; }
  .yooDataTableWrap .btn {
    position: absolute;
    padding: 0.5em 1.4em;
    z-index: 1; }
    .yooDataTableWrap .btn .yoo-add {
      font-size: 22px;
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      margin-top: -3px;
      margin-bottom: -2px;
      position: relative;
      top: 2px; }
    .yooDataTableWrap .btn.yoo-table-btn1 {
      top: 0;
      right: 20px; }
    .yooDataTableWrap .btn.yoo-table-btn2 {
      right: 167px;
      top: 0;
      padding: 0.465em 1.4em;
      border-width: 1px;
      background-color: rgba(0, 0, 0, 0.05); }

#yooDataTable th:first-child,
#yooDataTable td:first-child {
  border-left: none;
  width: 1% !important; }
#yooDataTable th:last-child,
#yooDataTable td:last-child {
  border-right: none;
  width: 1% !important;
  text-align: center; }
#yooDataTable th:nth-child(2),
#yooDataTable td:nth-child(2) {
  width: 1% !important; }
#yooDataTable th:nth-child(3),
#yooDataTable td:nth-child(3) {
  width: 16% !important; }
#yooDataTable th:nth-child(5),
#yooDataTable td:nth-child(5) {
  width: 15% !important; }
#yooDataTable th:nth-child(6),
#yooDataTable td:nth-child(6) {
  width: 16% !important; }
#yooDataTable th:nth-child(7),
#yooDataTable td:nth-child(7) {
  width: 12% !important; }
#yooDataTable th:nth-child(8),
#yooDataTable td:nth-child(8) {
  width: 1% !important; }
#yooDataTable th:nth-child(9),
#yooDataTable td:nth-child(9) {
  width: 5% !important; }

.dataTables_wrapper .top {
  padding-right: 309px; }

@media screen and (max-width: 1500px) {
  #yooDataTable th:nth-child(3),
  #yooDataTable td:nth-child(3) {
    width: 20% !important; } }
@media screen and (max-width: 991px) {
  #yooDataTable_wrapper .dataTable tbody th,
  #yooDataTable_wrapper .dataTable tbody td,
  #yooDataTable_wrapper .dataTable thead th,
  #yooDataTable_wrapper .dataTable thead td {
    padding: 10px 10px; }

  .yoo-filter-btn {
    right: 2px;
    top: 14px; } }
@media screen and (max-width: 767px) {
  .yooDataTableWrap .btn {
    position: initial;
    margin-left: 20px;
    margin-bottom: 15px; }

  #yooDataTable_wrapper {
    overflow-x: auto;
    overflow-y: hidden;
    margin-top: 50px;
    position: initial; }
    #yooDataTable_wrapper .top {
      padding-right: 10px;
      position: absolute;
      left: 0px;
      top: 50px;
      width: 100%; }
    #yooDataTable_wrapper .dataTables_filter {
      margin-top: 0 !important; }
    #yooDataTable_wrapper .dataTables_paginate {
      width: 68px;
      margin-left: auto;
      margin-top: 0;
      float: right; }
    #yooDataTable_wrapper .dataTables_wrapper .dataTables_info {
      float: right !important; } }
@media screen and (max-width: 767px) {
  .dataTables_wrapper .dataTables_filter input {
    width: 130px; }

  .yooDataTableWrap .btn {
    padding: 0.5em 1em;
    margin-left: 3px;
    font-size: 13px; }
    .yooDataTableWrap .btn:first-child {
      margin-left: 20px; }

  .yooDataTableWrap .btn.yoo-table-btn2 {
    padding: 0.465em 1em; } }
/*========== Start Chartting  ==========*/
.yoo-chat-heading {
  height: 81px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid #e5e5e5; }

.yoo-user-meta {
  color: rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 2px; }
  .yoo-user-meta li:not(:last-child) {
    margin-right: 10px;
    padding-right: 10px;
    position: relative; }
    .yoo-user-meta li:not(:last-child):before {
      content: '';
      position: absolute;
      height: 15px;
      width: 1px;
      background: #e5e5e5;
      right: 0;
      top: 50%;
      margin-top: -7px; }
  .yoo-user-meta a:hover {
    color: rgba(0, 0, 0, 0.7); }
  .yoo-user-meta .yoo-get-star {
    font-size: 18px;
    height: 18px;
    width: 18px; }

.yoo-chatbox-wrap {
  height: calc(100vh - 141px);
  padding: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end; }
  .yoo-chatbox-wrap .yoo-chat-box {
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 15px; }
  .yoo-chatbox-wrap .yoo-user.yoo-style2 {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    padding-bottom: 10px; }
    .yoo-chatbox-wrap .yoo-user.yoo-style2 .yoo-user-name {
      margin-bottom: 8px; }
    .yoo-chatbox-wrap .yoo-user.yoo-style2 .yoo-user-time {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.4);
      margin-left: 8px;
      font-weight: 400; }
  .yoo-chatbox-wrap .yoo-user-chat-text {
    font-size: 14px; }
  .yoo-chatbox-wrap .yoo-user-chat-text p {
    margin-bottom: 6px; }
    .yoo-chatbox-wrap .yoo-user-chat-text p:last-child {
      margin-bottom: 0; }

.yoo-chat-controller {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border: 2px solid #e5e5e5;
  border-radius: 5px; }
  .yoo-chat-controller .yoo-chat-input {
    border: none;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    width: 100%;
    width: 100%;
    padding: 15px 20px;
    font-size: 14px; }
    .yoo-chat-controller .yoo-chat-input:focus {
      outline: none; }
    .yoo-chat-controller .yoo-chat-input::-webkit-input-placeholder {
      color: rgba(0, 0, 0, 0.4); }
    .yoo-chat-controller .yoo-chat-input::-moz-placeholder {
      color: rgba(0, 0, 0, 0.4); }
    .yoo-chat-controller .yoo-chat-input:-ms-input-placeholder {
      color: rgba(0, 0, 0, 0.4); }
    .yoo-chat-controller .yoo-chat-input:-moz-placeholder {
      color: rgba(0, 0, 0, 0.4); }
  .yoo-chat-controller .yoo-chat-option {
    font-size: 24px;
    height: 100%;
    width: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    height: 50px;
    color: rgba(0, 0, 0, 0.4); }
  .yoo-chat-controller .yoo-custom-input-area .yoo-custom-input-field {
    padding: 14px 15px;
    font-size: 14px;
    border-left: 2px solid #e5e5e5; }
    .yoo-chat-controller .yoo-custom-input-area .yoo-custom-input-field:focus {
      min-height: 50px; }
  .yoo-chat-controller .yoo-icon-group.yoo-style1 {
    padding: 13px 20px 13px 0; }
  .yoo-chat-controller .yoo-custom-input-area {
    overflow: auto;
    max-height: 300px; }

.yoo-chat-date {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  line-height: 1.2em;
  overflow: hidden;
  position: relative;
  margin-bottom: 25px; }
  .yoo-chat-date span {
    position: relative;
    padding: 0 12px; }
    .yoo-chat-date span:before, .yoo-chat-date span:after {
      content: '';
      position: absolute;
      top: 50%;
      height: 1px;
      width: 2000px;
      background-color: #e5e5e5; }
    .yoo-chat-date span:before {
      left: 100%; }
    .yoo-chat-date span:after {
      right: 100%; }

.yoo-messenger-sidebar .yoo-search-wrap {
  padding: 0 20px 20px; }
.yoo-messenger-sidebar .yoo-user-list .yoo-user-img img {
  height: 48px;
  width: 48px; }
.yoo-messenger-sidebar .yoo-user-list .yoo-user-name {
  font-size: 16px;
  line-height: 1.2em;
  font-weight: 400; }
.yoo-messenger-sidebar .yoo-user-text {
  font-size: 14px;
  line-height: 1.6em; }
.yoo-messenger-sidebar a.yoo-user.yoo-style1 {
  position: relative; }
.yoo-messenger-sidebar .yoo-user-list .yoo-user-time {
  position: absolute;
  right: 30px;
  top: 12px;
  font-size: 13px; }
.yoo-messenger-sidebar .yoo-email-friend-list-wrap .yoo-user-list .yoo-user-time {
  position: initial; }
.yoo-messenger-sidebar .yoo-nav-label {
  font-size: 10px;
  background-color: #ff3b30;
  margin-left: 4px;
  position: relative;
  top: -1px; }
.yoo-messenger-sidebar .yoo-online-status.yoo-live {
  font-size: 14px; }

.yoo-messenger-body .yoo-user.yoo-style2 .yoo-user-time {
  font-size: 14px; }
.yoo-messenger-body .yoo-chat-date {
  font-size: 14px;
  font-weight: 400; }
.yoo-messenger-body .yoo-user.yoo-style2 .yoo-user-chat-text-group {
  width: 60%; }
.yoo-messenger-body .yoo-user.yoo-style2 .yoo-user-chat-text-in {
  display: inline-block;
  font-size: 14px;
  line-height: 1.6em;
  border-radius: 10px;
  padding: 9px 15px;
  background-color: #e5e5e5;
  color: rgba(0, 0, 0, 0.95);
  position: relative; }
.yoo-messenger-body .yoo-chatbox-wrap .yoo-user-chat-text:not(:last-child) {
  margin-bottom: 10px; }
.yoo-messenger-body .yoo-user-chat-text-seting {
  position: absolute;
  left: 100%;
  top: 9px;
  margin-left: 13px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
.yoo-messenger-body .yoo-user-chat-text-in:hover .yoo-user-chat-text-seting {
  opacity: 1;
  visibility: visible; }
.yoo-messenger-body .yoo-user.yoo-style2.yoo-right-side .yoo-user-chat-text-group {
  margin-left: auto;
  text-align: right; }
.yoo-messenger-body .yoo-user.yoo-style2.yoo-right-side .yoo-user-chat-text-in {
  text-align: left;
  color: rgba(255, 255, 255, 0.9);
  background-color: #007aff; }
.yoo-messenger-body .yoo-right-side .yoo-user-chat-text-seting {
  left: initial;
  right: 100%;
  margin-left: 0;
  margin-right: 13px; }

.yoo-right-side .yoo-icon-group.yoo-style1 {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse; }
  .yoo-right-side .yoo-icon-group.yoo-style1 li {
    margin-left: 15px;
    margin-right: 0; }

@media screen and (max-width: 1199px) {
  .yoo-chat-heading {
    padding-left: 90px;
    padding-right: 20px; } }
@media screen and (max-width: 767px) {
  .yoo-chat-heading .yoo-user-meta {
    display: none; }

  .yoo-messenger-body .yoo-user.yoo-style2 .yoo-user-chat-text-group {
    width: 75%; } }
@media screen and (max-width: 575px) {
  .yoo-messenger-body .yoo-user.yoo-style2 .yoo-user-time {
    font-size: 13px;
    line-height: 1.2em; }

  .yoo-chat-heading .yoo-user.yoo-style2 .yoo-user-name {
    font-size: 13px; }

  .yoo-chat-heading .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 10px; }

  .yoo-chat-heading .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 10px; }

  .yoo-chat-heading .yoo-icon-group.yoo-style1 li a {
    font-size: 21px; }

  .yoo-chat-controller .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 10px; }

  .yoo-user.yoo-style4 .btn {
    min-width: initial; }

  .yoo-chatbox-wrap {
    padding: 15px; } }
@media screen and (max-width: 400px) {
  .yoo-chat-heading .yoo-icon-group.yoo-style1 li a,
  .yoo-chat-controller .yoo-icon-group.yoo-style1 li a {
    font-size: 19px; } }
.conversation-list {
  list-style: none;
  padding-left: 0; }

.conversation-list li {
  margin-bottom: 24px; }

.conversation-list .chat-avatar {
  width: 40px;
  display: inline-block;
  text-align: center;
  float: left; }

.conversation-list .chat-avatar i {
  font-size: 12px;
  font-style: normal; }

.conversation-list .ctext-wrap i {
  display: block;
  font-style: normal;
  font-weight: bold;
  position: relative;
  font-size: 12px;
  color: #2cb9b3; }

.conversation-list .conversation-text {
  display: inline-block;
  font-size: 12px;
  float: left;
  margin-left: 12px;
  width: 70%; }

.conversation-list .ctext-wrap {
  padding: 10px;
  background: #d5f2ef;
  border-radius: 3px;
  position: relative;
  display: inline-block; }

.conversation-list .ctext-wrap p {
  margin: 0px;
  padding-top: 3px; }

.conversation-list .ctext-wrap:after {
  right: 100%;
  top: 20%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(213, 242, 239, 0);
  border-right-color: #d5f2ef;
  border-width: 5px;
  margin-top: -5px; }

.conversation-list .odd .chat-avatar {
  float: right !important; }

.conversation-list .odd .conversation-text {
  width: 70% !important;
  margin-right: 12px;
  text-align: right;
  float: right !important; }

.conversation-list .odd .ctext-wrap {
  background: #eeeef2 !important; }

.conversation-list .odd .ctext-wrap i {
  color: #acacac; }

.conversation-list .odd .ctext-wrap:after {
  left: 100% !important;
  top: 20% !important;
  border-color: rgba(238, 238, 242, 0) !important;
  border-left-color: #eeeef2 !important; }

.chat-send {
  padding-left: 0px; }

.chat-send button {
  width: 100%; }

.yoo-chat-info-card {
  position: absolute;
  top: 81px;
  right: -460px;
  width: 450px;
  background-color: #fff;
  border-left: 1px solid #e5e5e5;
  height: calc(100vh - 141px);
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  overflow: auto; }

.yoo-chat-container {
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-chat-container.yoo-active .yoo-chat-info-card {
  right: 0; }

.yoo-chat-container.yoo-active {
  padding-right: 450px; }

.yoo-chat-info-cross {
  position: absolute;
  font-size: 21px;
  right: 15px;
  top: 15px;
  cursor: pointer;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }
  .yoo-chat-info-cross:hover {
    color: rgba(0, 0, 0, 0.95); }

.yoo-messenger-sidebar {
  width: 300px;
  background-color: #fff;
  height: calc(100vh - 60px);
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  left: 300px;
  top: 60px;
  border-right: 1px solid #e5e5e5;
  z-index: 50;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-messenger-sidebar .yoo-messenger-sidebar-in {
    height: 100%; }
  .yoo-messenger-sidebar .yoo-sidebar-nav {
    width: 100%; }

.yoo-chart-container {
  position: relative;
  margin-left: 300px; }

.yoo-people-toggle-btn {
  font-size: 32px;
  position: absolute;
  top: 0;
  left: 0;
  border-right: 2px solid #e5e5e5;
  width: 62px;
  height: 80px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(0, 0, 0, 0.4);
  display: none;
  z-index: 101; }
  .yoo-people-toggle-btn i,
  .yoo-people-toggle-btn .hydrated {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
  .yoo-people-toggle-btn.active i, .yoo-people-toggle-btn.active .hydrated {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg); }

@media screen and (max-width: 1500px) {
  .yoo-messenger-sidebar {
    left: 300px; } }
@media screen and (max-width: 1600px) {
  .yoo-chat-container.yoo-active {
    padding-right: 0; }

  .yoo-chat-container .yoo-chat-info-card {
    right: 0;
    display: block;
    width: 100%;
    z-index: 100; }

  .yoo-chat-container.yoo-active .yoo-chat-info-card {
    right: -460px;
    display: none; } }
@media screen and (max-width: 1199px) {
  .yoo-chart-container {
    margin-left: 0px; }

  .yoo-people-toggle-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }

  .yoo-messenger-sidebar {
    width: 300px;
    left: -300px; }
    .yoo-messenger-sidebar.active {
      left: 0; }
    .yoo-messenger-sidebar .yoo-email-user-wrap {
      padding-left: 75px; }

  .yoo-email-user-wrap {
    height: 81px;
    width: 100%;
    padding: 0 15px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding-left: 75px; } }
/*========== End Chartting  ==========*/
/*========== Start Live Chat  ==========*/
.yoo-live-chat-wrap {
  -webkit-box-shadow: 0px 4px 36px 4px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 4px 36px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  width: 100%;
  max-width: 320px;
  overflow: hidden; }

.yoo-live-chat-heading {
  background-color: #007aff;
  border-radius: 10px 10px 0 0;
  position: relative; }
  .yoo-live-chat-heading .yoo-live-chat-user {
    padding: 15px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    max-width: calc(100% - 50px);
    cursor: pointer; }
  .yoo-live-chat-heading .yoo-live-chat-user-img {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px; }
  .yoo-live-chat-heading .yoo-live-chat-user-name {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    margin-top: 3px; }
  .yoo-live-chat-heading .yoo-live-chat-user-text {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6em; }

.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 20px;
  border-bottom: 1px solid #e5e5e5;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list li:hover {
    background-color: rgba(0, 0, 0, 0.05); }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-chat-avatar {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px; }
  .yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-chat-avatar img {
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-chat-time {
  position: absolute;
  right: 20px;
  top: 10px;
  font-style: normal;
  font-size: 12px;
  line-height: 1.6em;
  color: rgba(0, 0, 0, 0.4); }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-conversation-user-name {
  font-size: 13px;
  margin-bottom: 1px;
  margin-top: 3px;
  font-weight: 400; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-unseen-msg .yoo-conversation-user-name {
  font-weight: 500; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list .yoo-conversation-text p {
  font-size: 12px;
  line-height: 1.3em;
  margin-bottom: 0; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-unseen-msg {
  background-color: rgba(0, 0, 0, 0.05); }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-live-chat-list {
  margin: 0;
  padding: 0;
  list-style: none;
  overflow: auto;
  height: 340px;
  border: 1px solid #e5e5e5;
  border-bottom: none; }

.yoo-live-chat-list-wrap.yoo-style1 .yoo-custom-input-area .yoo-custom-input-field,
.yoo-live-chat.yoo-style2 .yoo-custom-input-area .yoo-custom-input-field {
  height: 100%;
  width: 100%;
  padding: 15px 0 15px 20px;
  font-size: 13px;
  line-height: 1.6em;
  overflow: hidden; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-conversion-input,
.yoo-live-chat.yoo-style2 .yoo-conversion-input {
  border: 1px solid #e5e5e5;
  border-radius: 0 0 10px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-icon-group.yoo-style1,
.yoo-live-chat.yoo-style2 .yoo-icon-group.yoo-style1 {
  padding: 14px 20px 14px 5px; }
.yoo-live-chat-list-wrap.yoo-style1 .yoo-icon-group.yoo-style1 li:not(:last-child),
.yoo-live-chat.yoo-style2 .yoo-icon-group.yoo-style1 li:not(:last-child) {
  margin-right: 8px; }

.yoo-live-chat.yoo-style2 .yoo-conversation-list {
  margin: 0;
  padding: 0;
  list-style: none;
  overflow: auto;
  height: 340px;
  border: 1px solid #e5e5e5;
  border-bottom: none; }
.yoo-live-chat.yoo-style2 .yoo-conversation-list {
  padding: 15px 0; }
.yoo-live-chat.yoo-style2 .yoo-conversation-list li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  padding: 0px 20px;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  text-align: right; }
  .yoo-live-chat.yoo-style2 .yoo-conversation-list li p {
    display: inline-block; }
  .yoo-live-chat.yoo-style2 .yoo-conversation-list li img {
    display: inline-block;
    margin-bottom: 5px; }
.yoo-live-chat.yoo-style2 .yoo-conversation-list .yoo-another-side {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  background: transparent;
  text-align: left; }
  .yoo-live-chat.yoo-style2 .yoo-conversation-list .yoo-another-side p {
    background-color: #e5e5e5;
    color: rgba(0, 0, 0, 0.95); }
.yoo-live-chat.yoo-style2 .yoo-chat-avatar {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px; }
.yoo-live-chat.yoo-style2 .yoo-chat-time {
  display: none; }
.yoo-live-chat.yoo-style2 .yoo-conversation-text p {
  margin-bottom: 5px;
  font-size: 13px;
  line-height: 1.6em;
  background-color: #007aff;
  padding: 5px 10px;
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.9); }

.yoo-toggle-chat-btn {
  height: 48px;
  width: 48px;
  background-color: #0077fc;
  color: rgba(255, 255, 255, 0.9);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  font-size: 24px;
  position: relative;
  cursor: pointer; }
  .yoo-toggle-chat-btn i,
  .yoo-toggle-chat-btn .hydrated {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%); }
    .yoo-toggle-chat-btn i:last-child,
    .yoo-toggle-chat-btn .hydrated:last-child {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0);
              transform: translate(-50%, -50%) scale(0); }

.yoo-toggle-chat-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 101; }
  .yoo-toggle-chat-wrap .yoo-live-chat-wrap {
    width: 320px;
    position: absolute;
    right: 0;
    bottom: 100%;
    background-color: #fff;
    margin-bottom: -15px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease; }
  .yoo-toggle-chat-wrap.yoo-active .yoo-live-chat-wrap {
    margin-bottom: 15px;
    opacity: 1;
    visibility: visible; }
  .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn i,
  .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn .hydrated {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%); }
    .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn i:first-child,
    .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn .hydrated:first-child {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0);
              transform: translate(-50%, -50%) scale(0); }
    .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn i:last-child,
    .yoo-toggle-chat-wrap.yoo-active .yoo-toggle-chat-btn .hydrated:last-child {
      opacity: 1;
      -webkit-transform: translate(-50%, -50%) scale(1);
              transform: translate(-50%, -50%) scale(1); }

.yoo-live-chat-heading-btn {
  cursor: pointer;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  right: 15px;
  font-size: 18px;
  opacity: 0.7;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-live-chat-heading-btn:hover {
    color: white; }

.yoo-live-chat-list-btns {
  font-size: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  padding: 0px 20px;
  border-top: 1px solid #e5e5e5;
  color: rgba(0, 0, 0, 0.4); }
  .yoo-live-chat-list-btns a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 15px; }

.yoo-live-chat-body {
  position: relative; }

.yoo-live-chat-list-wrap {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #fff;
  border-radius: 0 0 10px 10px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-live-chat-wrap.yoo-active .yoo-live-chat-list-wrap {
  left: 100%; }

.yoo-user-arrow-btn {
  color: #fff;
  opacity: 0.7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 0;
  margin-left: 0px;
  margin-right: 0px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease; }

.yoo-live-chat-wrap.yoo-active .yoo-user-arrow-btn {
  font-size: 18px;
  margin-left: -5px;
  margin-right: 15px; }

.yoo-chat-conversation.yoo-live-chat.yoo-style2 {
  position: relative;
  padding-bottom: 52px; }
  .yoo-chat-conversation.yoo-live-chat.yoo-style2 .yoo-conversion-input {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    background: #fff;
    overflow: auto; }

.yoo-messenger-body {
  background-color: #fff; }

/*========== End Live Chat  ==========*/
/*========== Start Profile Page  ==========*/
.yoo-profile-thumb {
  height: 400px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  padding: 0 30px;
  position: relative; }
  .yoo-profile-thumb.yoo-small {
    height: 100px; }

.yoo-profile-sidebar-body {
  height: calc(100vh - 100px); }

.yoo-profile-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: -40px;
  position: relative;
  z-index: 1; }
  .yoo-profile-info .yoo-profile-text {
    margin-bottom: 12px; }
  .yoo-profile-info .yoo-profile-pic {
    height: 160px;
    width: 160px;
    border: 4px solid rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    position: relative; }
  .yoo-profile-info .yoo-profile-name {
    font-size: 24px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 1px; }
  .yoo-profile-info .yoo-profile-email {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8); }
  .yoo-profile-info .yoo-overlay.yoo-style1 {
    top: initial;
    bottom: 0;
    height: 120px; }
  .yoo-profile-info.yoo-small {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center;
    margin-bottom: 0;
    margin-top: -35px; }
    .yoo-profile-info.yoo-small .yoo-profile-pic {
      height: 70px;
      width: 70px;
      border-width: 2px;
      margin-bottom: 0;
      margin-right: 0; }
    .yoo-profile-info.yoo-small .yoo-profile-text {
      padding: 6px 0 10px; }
    .yoo-profile-info.yoo-small .yoo-profile-name {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.95); }
    .yoo-profile-info.yoo-small .yoo-profile-email {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.7); }

.yoo-drop-toggle.yoo-style2 {
  margin-right: -7px; }
  .yoo-drop-toggle.yoo-style2 .yoo-toggle-btn {
    font-size: 21px;
    color: rgba(0, 0, 0, 0.4);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer; }
    .yoo-drop-toggle.yoo-style2 .yoo-toggle-btn:hover {
      color: rgba(0, 0, 0, 0.7); }

.yoo-about-text {
  font-size: 14px;
  line-height: 1.6em; }

.yoo-post-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between; }
  .yoo-post-option .yoo-icon-group.yoo-style1 {
    margin: 0;
    padding: 0; }
    .yoo-post-option .yoo-icon-group.yoo-style1:before {
      display: none; }
  .yoo-post-option .yoo-btn {
    font-weight: 500;
    background-color: rgba(0, 0, 0, 0.05); }
    .yoo-post-option .yoo-btn:hover {
      background-color: transparent; }

.yoo-profile-sidebar {
  width: 340px;
  background-color: #fff;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  right: 0;
  top: 0;
  border-left: 1px solid #e5e5e5;
  padding-bottom: 40px;
  padding-top: 60px;
  z-index: 5; }
  .yoo-profile-sidebar .yoo-sidebar-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 339px;
    padding: 0 30px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    border-top: 1px solid #e5e5e5;
    background-color: #fff; }
    .yoo-profile-sidebar .yoo-sidebar-footer .yoo-search.yoo-style2 {
      -webkit-box-flex: 1;
          -ms-flex: 1;
              flex: 1;
      min-width: initial; }
    .yoo-profile-sidebar .yoo-sidebar-footer .yoo-search.yoo-style2 button {
      font-size: 21px;
      width: 30px;
      left: -8px; }
    .yoo-profile-sidebar .yoo-sidebar-footer .yoo-search.yoo-style2 input {
      height: 42px;
      padding-left: 23px;
      border: none; }
    .yoo-profile-sidebar .yoo-sidebar-footer .yoo-icon-group-icon {
      font-size: 21px; }
    .yoo-profile-sidebar .yoo-sidebar-footer .yoo-icon-group.yoo-style1 li:not(:last-child) {
      margin-right: 13px; }
  .yoo-profile-sidebar .yoo-user-list .yoo-user.yoo-style1 {
    padding: 5px 30px; }

.yoo-profile-story {
  height: 155px;
  width: 115px;
  border-radius: 4px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-profile-story .yoo-profile-pic {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.6);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease; }
  .yoo-profile-story .yoo-profile-email {
    font-size: 13px;
    font-weight: 500;
    color: #fff;
    margin-top: 1px;
    margin-bottom: 10px; }
  .yoo-profile-story:hover .yoo-profile-pic {
    border-color: #fff; }

.yoo-story-slider .swiper-slide {
  width: 115px;
  margin-right: 10px; }

.yoo-profile-setting-container {
  max-width: 750px;
  margin: auto;
  padding-left: 20px;
  padding-right: 20px; }

.yoo-profile-setting-heading {
  text-align: center; }

.yoo-profile-setting-title {
  font-size: 24px;
  margin-bottom: 1px; }

.yoo-profile-setting-subtitle {
  font-size: 16px;
  line-height: 1.6em; }

.yoo-setting-profile-pic {
  border: 1px solid #e5e5e5;
  display: inline-block;
  border-radius: 4px;
  padding: 5px;
  position: relative; }

.yoo-profile-pic-edit-btn {
  position: absolute;
  top: 11px;
  right: 11px;
  height: 32px;
  width: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 18px;
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease; }
  .yoo-profile-pic-edit-btn:hover {
    color: white;
    background-color: rgba(0, 0, 0, 0.95); }

.yoo-setting-profile-pic:hover .yoo-profile-pic-edit-btn {
  opacity: 1; }

.yoo-profile-btn.yoo-style1 {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 15px;
  line-height: 1.6em;
  font-weight: 500;
  padding: 0.55em 1.5em;
  min-width: 135px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 1px solid transparent;
  border-radius: 4px; }
  .yoo-profile-btn.yoo-style1.yoo-color1 {
    border-color: #8e8e93;
    background-color: #8e8e93;
    color: rgba(255, 255, 255, 0.9); }
    .yoo-profile-btn.yoo-style1.yoo-color1:hover {
      opacity: 0.8; }
  .yoo-profile-btn.yoo-style1.yoo-color2 {
    border-color: #e5e5e5;
    background-color: rgba(0, 0, 0, 0.05);
    color: rgba(0, 0, 0, 0.7); }
    .yoo-profile-btn.yoo-style1.yoo-color2:hover {
      background-color: transparent; }

.yoo-profile-btn-group.yoo-style1 {
  padding: 0 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end; }
  .yoo-profile-btn-group.yoo-style1 .yoo-profile-btn:not(:last-child) {
    margin-right: 15px; }

.yoo-profile-setting-list-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px; }

.yoo-profile-setting-list li {
  font-size: 13px;
  margin-bottom: 5px; }
  .yoo-profile-setting-list li:last-child {
    margin-bottom: 0; }
.yoo-profile-setting-list .yoo-card-number {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-profile-setting-list .yoo-card-number span {
    margin-right: 8px; }

.yoo-invoice-table.yoo-style1 .yoo-invoice-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 0; }
.yoo-invoice-table.yoo-style1 .yoo-invoice-row-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
.yoo-invoice-table.yoo-style1 .yoo-invoice-icon {
  margin-right: 10px;
  max-width: 30px;
  border-radius: 4px;
  overflow: hidden; }
.yoo-invoice-table.yoo-style1 .yoo-invoice-text {
  font-size: 14px; }
.yoo-invoice-table.yoo-style1 .yoo-bill-amount {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.95); }
.yoo-invoice-table.yoo-style1 .yoo-invoice-total-bill {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  font-size: 14px;
  padding: 10px 0; }
  .yoo-invoice-table.yoo-style1 .yoo-invoice-total-bill .yoo-invoice-total-bill-text {
    color: rgba(0, 0, 0, 0.4);
    margin-right: 6px; }
  .yoo-invoice-table.yoo-style1 .yoo-invoice-total-bill .yoo-invoice-total-bill-amount {
    color: rgba(0, 0, 0, 0.95);
    font-weight: 500; }

.yoo-post-view-imoges {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 4px; }
  .yoo-post-view-imoges .yoo-post-imog {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-left: -4px;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid #fff; }

.yoo-post-view-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .yoo-post-view-wrap .yoo-post-view-imoges {
    margin-right: 5px; }

.yoo-newsfeed-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: -15px;
  margin-right: -15px; }
  .yoo-newsfeed-row .yoo-newsfeed-col-1 {
    width: 320px;
    padding-left: 15px;
    padding-right: 15px;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
  .yoo-newsfeed-row .yoo-newsfeed-col-2 {
    width: 100%;
    padding-left: 15px;
    padding-right: 15px; }

.yoo-profile-sidebar-btn {
  height: 48px;
  width: 48px;
  background-color: #0077fc;
  color: rgba(255, 255, 255, 0.9);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  font-size: 24px;
  position: relative;
  cursor: pointer;
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: none; }

.yoo-profile-sidebar-cross {
  position: absolute;
  right: 15px;
  top: 68px;
  font-size: 20px;
  color: #b5b5b5;
  display: none;
  z-index: 2; }

.yoo-form-field-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.95);
  margin-bottom: 4px;
  display: block; }

.yoo-form-field-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }
  .yoo-form-field-wrap.yoo-style1 .yoo-form-field-label {
    padding: 0px 20px 0 0;
    width: 160px;
    text-align: right;
    margin: 0;
    -webkit-box-flex: 0;
        -ms-flex: none;
            flex: none; }
    .yoo-form-field-wrap.yoo-style1 .yoo-form-field-label.yoo-type1 {
      min-height: 36px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: end;
          -ms-flex-pack: end;
              justify-content: flex-end;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
  .yoo-form-field-wrap.yoo-style1 .yoo-select {
    width: 100%; }

.yoo-switch-wrap.yoo-style1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.custom-control.custom-radio.form-check-inline.yoo-type1 {
  margin-top: 7px; }

.custom-control.custom-radio.form-check-inline.yoo-type1 img {
  margin-top: -6px; }

.yoo-with-icon {
  position: relative; }
  .yoo-with-icon .yoo-input-icon {
    position: absolute;
    left: 15px;
    top: 9px;
    z-index: 4;
    pointer-events: none;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.4); }
  .yoo-with-icon .form-control {
    padding-left: 50px;
    border-radius: 7px !important; }
  .yoo-with-icon.form-group-sm .yoo-input-icon {
    top: 7px;
    font-size: 18px; }

.nav-tabs .nav-link .yoo-tab-icon {
  color: rgba(0, 0, 0, 0.4);
  font-size: 24px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 10px; }

.nav.nav-tabs.yoo-setting-tab .nav-link {
  font-size: 15px; }

.nav.nav-tabs.yoo-setting-tab {
  background-color: #f0f0f0; }

@media screen and (max-width: 1600px) {
  .yoo-horizontal-list.yoo-style2 li {
    margin: 0 15px; } }
@media screen and (max-width: 1500px) {
  .yoo-newsfeed-row .yoo-newsfeed-col-1 {
    width: 280px; }

  .yoo-profile-sidebar,
  .yoo-profile-sidebar .yoo-sidebar-footer {
    right: -340px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease; }

  .yoo-profile-sidebar-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }

  .yoo-profile-sidebar.active,
  .yoo-profile-sidebar.active .yoo-sidebar-footer {
    right: 0; }

  .yoo-profile-sidebar.active .yoo-profile-sidebar-btn {
    display: none; }

  .yoo-profile-sidebar.active .yoo-profile-sidebar-cross {
    display: block; }

  .yoo-profile-content .yoo-content.yoo-style3 {
    padding-right: 0;
    overflow: hidden; } }
@media screen and (max-width: 1199px) {
  .yoo-profile-sidebar .yoo-sidebar-footer .yoo-search.yoo-style2 {
    position: relative; } }
@media screen and (max-width: 991px) {
  .nav.nav-tabs.yoo-setting-tab .nav-link {
    font-size: 14px;
    padding: 7px 12px; }
    .nav.nav-tabs.yoo-setting-tab .nav-link:before, .nav.nav-tabs.yoo-setting-tab .nav-link:after {
      display: none; }

  .nav-tabs .nav-link .yoo-tab-icon {
    margin-right: 7px; } }
@media screen and (max-width: 767px) {
  .yoo-newsfeed-row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }

  .yoo-newsfeed-row .yoo-newsfeed-col-1 {
    width: 100%;
    margin-bottom: 30px; }

  .yoo-profile-info {
    margin-bottom: 30px; } }
@media screen and (max-width: 500px) {
  .yoo-profile-post-meta {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; } }
/*========== End Profile Page  ==========*/

/*# sourceMappingURL=iDashboard.css.map */