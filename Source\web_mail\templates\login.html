<!DOCTYPE html>
<html class="no-js" lang="en">

<head>
  <!-- Meta Tags -->
  <meta charset="utf-8" />
  <meta http-equiv="x-ua-compatible" content="ie=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="author" content="YooKits" />
  <!-- Page Title -->
  <title>Login</title>
  <!-- Favicon Icon -->
  <link rel="icon" href="/static/assets/img/favicon.png">
  <!-- CSS -->
  <link rel="stylesheet" type="text/css" href="/static/assets/css/bootstrap.min.css" />
  <link rel="stylesheet" type="text/css" href="/static/assets/css/fontawesome.css" />
  <link rel="stylesheet" type="text/css" href="/static/assets/css/iDashboard.css" />
</head>

<body class="yoo-white-bg">
  <div class="yoo-login-wrap yoo-style1">
    <div class="container">
      <div class="row">
        <div class="col-lg-7">
          <div class="yoo-vertical-middle">
            <div class="yoo-vertical-middle-in">
              <div class="yoo-signup-img yoo-style1">
                <img src="/static/assets/img/signup/01.png" alt="">
              </div>
            </div>
          </div>
        </div><!-- .col -->
        <div class="col-lg-5">
          <div class="yoo-vertical-middle">
            <div class="yoo-vertical-middle-in">
              <form class="yoo-form yoo-style1" onsubmit="event.preventDefault()">
                <h2 class="yoo-form-title">Đăng nhập để tiếp tục</h2>
                <div class="yoo-form-subtitle">Chưa có tài khoản? <a href="/signup.html"
                    class="yoo-form-btn yoo-style2">Đăng ký</a></div>
                <div class="yoo-height-b25 yoo-height-lg-b25"></div>
                <!-- <ul class="yoo-social-area yoo-style1 yoo-mp0">
                  <li><a href="#" class="yoo-form-btn yoo-style1 yoo-colo2"><i class="fab fa-facebook-f"></i><span>Sign up with Facebook</span></a></li>
                  <li><a href="#" class="yoo-form-btn yoo-style1 yoo-colo3"><i class="fab fa-google-plus-g"></i><span>Sign up with Gmail</span></a></li>
                </ul>
                <div class="yoo-height-b15 yoo-height-lg-b15"></div>
                <div class="yoo-form-separator">Or</div>
                <div class="yoo-height-b15 yoo-height-lg-b15"></div> -->
                <div class="row">
                  <div class="col-lg-12">
                    <div class="form-group level-up form-group-md">
                      <label for="user">Tài khoản (admin)</label>
                      <input type="text" class="form-control" id="user">
                    </div>
                  </div>
                  <div class="col-lg-12">
                    <div class="form-group level-up form-group-md">
                      <label for="pass">Mật khẩu (admin)</label>
                      <input type="password" class="form-control" id="pass">
                    </div>
                  </div>
                  <div class="col-lg-12">
                    <div class="form-group">
                      <div class="yoo-forget-pass-wrap">
                        <div class="custom-control custom-checkbox">
                          <input class="custom-control-input" type="checkbox" id="gridCheck">
                          <label class="custom-control-label" for="gridCheck">
                            <span class="custom-control-shadow"></span>Lưu tài khoản
                          </label>
                        </div>
                        <a href="#" class="yoo-form-btn yoo-style2">Quên mật khẩu?</a>
                      </div>
                    </div>
                    <button onclick="Login(event)" class="yoo-form-btn yoo-style1 yoo-color1"
                    style="background-color: #04AA6D;" class="yoo-form-btn yoo-style2 yoo-type1"><span>Đăng nhập</span></button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div><!-- .container -->
  </div>
  <script>
    window.localStorage.setItem("token", "");
    function Login() {
      var person = {
        username: $("#user").val(),
        password: $("#pass").val()
      }
      $.ajax({
        url: "/login",
        type: "post",
        dataType: "json",
        data: JSON.stringify(person),
        contentType: 'application/json',
        success: function (result) {
          var msg = result.mess;
          if (msg == "success") {
            var token = result.token
            toastr.success("Đăng nhập thành công");
            window.location.href = "/index.html";
            window.localStorage.setItem("token", token);
          } else {
            toastr.error(msg);
          }
        }
      });
    }

  </script>
  <!-- Required Scripts -->

  <script src="/static/assets/js/jquery-1.12.4.min.js"></script>
  <script src="/static/assets/js/bootstrap.bundle.min.js"></script>
  <script src="/static/assets/js/smooth-scrollbar.js"></script>
  <script src="/static/assets/js/iDashboard.js"></script>
  <script src="https://unpkg.com/ionicons@5.0.0/dist/ionicons.js"></script>
  <link href="/static/toast/toastr.css" rel="stylesheet" />
  <script src="/static/toast/toastr.min.js"></script>

  <script src="/static/myscript.js"></script>
</body>

</html>