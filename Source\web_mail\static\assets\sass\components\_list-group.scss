.list-group-item {
  font-size: 16px;
  line-height: 1.6em;
  border-color: $base-color4;
  color: $base-color1;
  padding: 12px 20px;
}

.list-group-item.active,
.list-group-item-action.active:hover {
  border-color: rgba($blue-color, 0.1);
  background-color: rgba($blue-color, 0.1);
  color: $blue-color;
}

.list-group-item.disabled,
.list-group-item:disabled {
  color: $base-color3;
}

.list-group-item-action:focus,
.list-group-item-action:hover {
  color: $blue-color;
  background-color: rgba($blue-color, 0.1);
  border-color: rgba($blue-color, 0.1);
}

.list-group-item-primary {
  background-color: $blue-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-secondary {
  background-color: $gray-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-success {
  background-color: $green-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-danger {
  background-color: $red-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-warning {
  background-color: $orange-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-info {
  background-color: $light-blue-color;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-light {
  background-color: $base-color5;
  color: $base-color2;
  border: none;
  letter-spacing: 0.17px;
}

.list-group-item-dark {
  background-color: $base-color1;
  color: #fff;
  border: none;
  letter-spacing: 0.17px;
}

.list-unstyled {
  list-style: disc;
  padding-left: 17px;
  margin-bottom: 20px;

  li {
    font-size: 16px;
    line-height: 1.6em;
    margin-bottom: 3px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  ul {
    margin-top: 3px;
    list-style: circle;
  }
}

.list-inline {
  margin-bottom: 20px;

  li {
    font-size: 16px;
    line-height: 1.6em;
  }
}

.yoo-list-group.yoo-style1 {
  li {
    margin-left: 45px;
  }

  li:not(:last-child) {
    border-bottom: 1px solid $base-color4;
    padding-bottom: 10px;
    margin-bottom: 10px;
  }

  &.yoo-type1 {
    li {
      margin-left: 63px;
    }
  }
}

.yoo-list-group.yoo-style2 {
  display: flex;
  flex-wrap: wrap;

  li:not(:last-child) {
    border-right: 1px solid $base-color4;
    margin-right: 20px;
    padding-right: 20px;
  }
}

.yoo-list-group.yoo-style3 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-left: -8px;
  margin-right: -8px;

  li {
    padding: 0 8px;
    width: 25%;
  }
}

.list-group-flush .list-group-item {
  border-left: 1px solid $base-color4;
  border-right: 1px solid $base-color4;
  margin-left: -1px;
  margin-right: -1px;
}

.list-group-flush .list-group-item:first-child {
  border-top-left-radius: .25rem;
  border-top-right-radius: .25rem;
}

.list-group-flush .list-group-item:last-child {
  border-bottom-right-radius: .25rem;
  border-bottom-left-radius: .25rem;
}

@media screen and (max-width: 575px) {
  .yoo-list-group.yoo-style3 li {
    width: 33.333333%;
  }
}
