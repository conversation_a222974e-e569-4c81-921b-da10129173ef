<!DOCTYPE html>
<html class="no-js" lang="en">

{% include 'include/header.html' %}
<style>
  body {
    font-family: Arial, Helvetica, sans-serif;
  }

  * {
    box-sizing: border-box;
  }

  body input {
    border: none;
  }

  body input:focus {
    outline: none;
  }




  /* Set a style for all buttons */
  button {
    background-color: #04AA6D;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
    width: 100%;
    opacity: 0.9;
  }

  button:hover {
    opacity: 1;
  }

  /* Float cancel and delete buttons and add an equal width */
  .cancelbtn,
  .deletebtn {
    float: left;
    width: 50%;
  }

  /* Add a color to the cancel button */
  .cancelbtn {
    background-color: #ccc;
    color: black;
  }

  /* Add a color to the delete button */
  .deletebtn {
    background-color: #f44336;
  }

  /* Add padding and center-align text to the container */
  .container {
    padding: 16px;
    text-align: center;
  }

  /* The Modal (background) */
  .modal {
    display: none;
    /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 1;
    /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    overflow: auto;
    /* Enable scroll if needed */
    background-color: #474e5d;
    padding-top: 50px;
  }

  /* Modal Content/Box */
  .modal-content {
    background-color: #fefefe;
    margin: 5% auto 15% auto;
    /* 5% from the top, 15% from the bottom and centered */
    border: 1px solid #888;
    width: 80%;
    /* Could be more or less, depending on screen size */
  }

  /* Style the horizontal ruler */
  hr {
    border: 1px solid #f1f1f1;
    margin-bottom: 25px;
  }

  /* The Modal Close Button (x) */
  .close {
    position: absolute;
    right: 35px;
    top: 15px;
    font-size: 40px;
    font-weight: bold;
    color: #f1f1f1;
  }

  .close:hover,
  .close:focus {
    color: #f44336;
    cursor: pointer;
  }

  /* Clear floats */
  .clearfix::after {
    content: "";
    clear: both;
    display: table;
  }

  /* Change styles for cancel button and delete button on extra small screens */
  @media screen and (max-width: 300px) {

    .cancelbtn,
    .deletebtn {
      width: 100%;
    }
  }

  .display_heading {
    display: flex;
    justify-content: space-between;
  }

  ._1MmTVs {
    padding: 12px 0;
    margin: 5px 0;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 1px 0 rgb(0 0 0 / 5%);
    color: #212121;
    background: #eaeaea;
    /* border-radius: 10px 10px 0 10px; */
    border-radius: 10px;
  }

  .imagePrd {
    border: 1px solid lightgray;
    margin-left: 5px;
    margin-right: 10px;
  }

  ._1MmTVs>input {
    flex: 1;
    font-size: 14px;
    line-height: 16px;
    border: 0;
    outline: none;
    background-color: inherit;
  }

  ._1MmTVs>svg {
    margin: 0 15px;
    stroke: #bbb;
  }
</style>

<body>
  <div class="yoo-height-b60 yoo-height-lg-b60"></div>
  <div class="yoo-headersidebar-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div>
  <!-- .yoo-headersidebar-toggle -->
  {% include 'include/headersidebar.html' %}

  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title">Check mail</h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Nhập mail</h2>
              </div>

            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <form>
                  <div class="form-group level-up">
                    <label for="exampleFormControlTextarea1"></label>
                    <textarea class="form-control" style="max-height: none;" id="input" rows="15"
                      placeholder="Nhập mỗi email 1 dòng. Định dạng: email|..."></textarea>
                  </div>
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-lg"
                    onclick="check(event)">Check now</button>
                  <br>
                  <br>
                </form>
                <div class="yoo-height-b5 yoo-height-lg-b5"></div>
              </div>
            </div>



          </div>
        </div>

      </div>
      <br>
      <div class="row">
        <div class="col-lg-12">

          <div class="yoo-card yoo-style1">

            <div class="yoo-card-heading">

              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Output</h2>
              </div>
              <b><span class="" id="liveverify" style="color:blue; margin-top : 20px ; padding:0 ;"></span></b>


            </div>
            <!-- <div class="col-md-2">

              </div>
              <div class="col-md-2">

              </div> -->




            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th scope="col" width="2%">#</th>
                      <th scope="col" width="10%">email</th>
                      <th scope="col" width="15%">password</th>
                      <th scope="col" width="15%">emailrecovery</th>
                      <th scope="col" width="5%">status</th>
                      <th scope="col" width="5%">birthday</th>
                      <th scope="col" width="5%">phonerecovery</th>
                      <th scope="col" width="5%">groups</th>
                      <th scope="col" width="10%">datecreate</th>
                      <th scope="col" width="5%">updater</th>
                      <th scope="col" width="15%">proxy</th>
                      <th scope="col" width="5%">proxylist</th>
                      <th scope="col" width="15%">#</th>
                    </tr>
                  </thead>
                  <tbody id="files"></tbody>
                </table>

                <div id="page" style="display:flex ; justify-content: center;"></div>


              </div>
            </div>
          </div>
        </div>

      </div>
      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>


      {% include 'include/footer.html' %}
      <!-- .yoo-card -->

    </div>
    <!-- .yoo-content -->
    <script>

      // Get the modal




      function Loadmail(page) {
        // var date = $("#combongaytaomail").val() //doma
        var status = $("#combostatus").val()
        var device = $("#combodevice").val()
        var search = window.location.search;
        // var page = "1";
        // var pages = Regex(search, /page=(\d+)/)
        // if (pages != null && pages.length > 1) {
        //   page = pages[1]
        // }
        $.ajax({
          // url: "/loadmail?page=" + page + "&datecreate=" + date + "&status=" + status + "&device=" + device, //doma
          url: "/loadmail?page=" + page + "&status=" + status + "&device=" + device,
          type: "get",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          dataType: "text",
          success: function (results) {
            const obj = JSON.parse(results);
            var max = obj.max;
            var count = obj.count
            var pagedu = count % max
            if (pagedu != 0) {
              pagedu = 1
            }
            var countpage = count / max + pagedu
            var result = obj.result;
            var stringToRender = ""
            var paginations = `<ul class="pagination pagination-lg">`

            var pageshow = pagination(page, countpage)


            for (let i = 0; i < pageshow.length; i++) {
              var pageht = pageshow[i]
              if (pageht == page || pageht == "...") {
                paginations = paginations +
                  `<li class="page-item disabled"><a class="page-link" href="#" tabindex="-1">${pageht}</a></li>`
              } else {
                paginations = paginations +
                  // `<li class="page-item"><a class="page-link" href="${window.location.pathname}?page=${pageht}">${pageht}</a></li>`
                  `<li class="page-item"><a class="page-link" style="color:blue" onclick="Loadmail(${pageht})">${pageht}</a></li>`
              }
            }
            paginations = paginations + `</ul>`
            let verify = 0;
            let x = 0;
            for (let i = 0; i < result.length; i++) {
              x = x + 1
              const onePost = result[i];
              if (Contains(onePost.status, "Verify") == true || Contains(onePost.status, "Disable") == true) {
                verify = verify + 1;
              }
              //  console.log(onePost)
              stringToRender +=
            //doma    // `<tr id="${onePost.id}"><td>${i + 1}</td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.email}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.password}"></td><td><input type="text" style="width:100%;" value="${onePost.emailrecovery}"></td><td><input type="text" value="${onePost.status}"></td><td><input type="text" value="${onePost.birthday}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.phonerecovery}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.groups}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.datecreate}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.updater}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxy}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxylist}"></td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a> | <a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Update("${onePost.id}");'>Update</a></td>`;
                `<tr id="${onePost.id}"><td>${i + 1}</td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.email}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.password}"></td><td><input type="text" style="width:100%;" value="${onePost.emailrecovery}"></td><td><input type="text" value="${onePost.status}"></td><td><input type="text" value="${onePost.backupcode}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.oldpassword}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.groups}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.chrome}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.updater}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxy}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxylist}"></td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a> | <a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Update("${onePost.id}");'>Update</a></td>`;
            }
            let tileveri = Math.ceil(verify / x * 100)
            let tilelive = 100 - tileveri
            $("#files").html(stringToRender);
            $("#page").html(paginations);
            $("#countmail").html("" + count + "");
            $("#liveverify").html("<span>&#9758;	</span>Verify: " + verify + " (" + tileveri + "%) <span>&#10159;</span> Live: " + (x - verify) + " (" + tilelive + "%)");
            $('input[value="undefined"]').val("");
            $('tr > *:nth-child(6)').hide();
            $('tr > *:nth-child(7)').hide();
            // $('tr > *:nth-child(10)').hide();
            $('tr > *:nth-child(12)').hide();
          },
          error: function (results) {
            window.location.href = "login.html"
            return
          }
        });
      }

      function Del(id) {
        var r = confirm("Xác nhận xóa!");
        if (r !== true) {
          return
        }
        $.ajax({
          url: "/maildelete/" + id,
          type: "get",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          dataType: "text",
          success: function (results) {
            const obj = JSON.parse(results);
            var result = obj.mess;
            if (result == "success") {
              toastr.success("Xóa thành công");
              document.getElementById(id).remove();
            } else {
              toastr.success(result);
            }
          }
        });
      }
      var varcheck = null;
      function check() {
        var person = {
          listmail: $('#input').val()
        }
        $.ajax({
          url: "checkmail",
          type: "post",
          dataType: "json",
          data: JSON.stringify(person),
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          contentType: 'application/json',
          success: function (obj) {
            // const obj = JSON.parse(results);
            var id = obj.id;
            if (id == undefined) {
              toastr.error(obj.mess);
              return;
            }
            varcheck = setInterval(() => getmailcheck(id), 1000);
          }
        });
      }
      function getmailcheck(id) {
        $.ajax({
          url: "getmailcheck?id=" + id,
          type: "get",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          contentType: 'application/json',
          success: function (obj) {
            var msg = obj.mess;
            if (msg == undefined) {
              msg = ""
            }
            // console.log("111111111" + "_" + msg)
            if (msg != "") {
              toastr.warning(msg);
              if (msg === "Id không tồn tại") {
                clearInterval(varcheck)
              }
            } else {
              toastr.success("Check mail xong")
              clearInterval(varcheck)
              var max = obj.max;
              var count = obj.count
              var pagedu = count % max
              if (pagedu != 0) {
                pagedu = 1
              }
              var countpage = count / max + pagedu
              var result = obj.result;
              var stringToRender = ""

              let verify = 0;
              let x = 0;
              for (let i = 0; i < result.length; i++) {
                x = x + 1
                const onePost = result[i];
                if (Contains(onePost.status, "Verify") == true || Contains(onePost.status, "Disable") == true) {
                  verify = verify + 1;
                }
                //  console.log(onePost)
                stringToRender +=
                //doma  // `<tr id="${onePost.id}"><td>${i + 1}</td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.email}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.password}"></td><td><input type="text" style="width:100%;" value="${onePost.emailrecovery}"></td><td><input type="text" value="${onePost.status}"></td><td><input type="text" value="${onePost.birthday}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.phonerecovery}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.groups}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.datecreate}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.updater}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxy}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxylist}"></td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a> | <a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Update("${onePost.id}");'>Update</a></td>`;
                  `<tr id="${onePost.id}"><td>${i + 1}</td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.email}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.password}"></td><td><input type="text" style="width:100%;" value="${onePost.emailrecovery}"></td><td><input type="text" value="${onePost.status}"></td><td><input type="text" value="${onePost.backupcode}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.oldpassword}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.groups}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.chrome}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.updater}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxy}"></td><td style="width:10%;"><input type="text" style="width:100%;" value="${onePost.proxylist}"></td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a> | <a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Update("${onePost.id}");'>Update</a></td>`;
              }
              let tileveri = Math.ceil(verify / x * 100)
              let tilelive = 100 - tileveri
              $("#files").html(stringToRender);

              $("#liveverify").html("<span>&#9758;	</span>Verify: " + verify + " (" + tileveri + "%) <span>&#10159;</span> Live: " + (x - verify) + " (" + tilelive + "%)");
              $('input[value="undefined"]').val("");
              $('tr > *:nth-child(6)').hide();
              $('tr > *:nth-child(7)').hide();
              // $('tr > *:nth-child(10)').hide();
              $('tr > *:nth-child(12)').hide();

            }
          }
        });
      }
      function Update(id) {
        var r = confirm("Xác nhận update!");
        if (r !== true) {
          return
        }
        var select = document.querySelectorAll('tr[id="' + id + '"] > td')

        var person = {
          email: select[1].childNodes[0].value,
          password: select[2].childNodes[0].value,
          emailrecovery: select[3].childNodes[0].value,
          status: select[4].childNodes[0].value,
        }
        $.ajax({
          url: "/mailedit/" + id,
          type: "post",
          dataType: "json",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          data: JSON.stringify(person),
          contentType: 'application/json',
          success: function (result) {

            // const obj = JSON.parse(result);
            var msg = result.mess;

            if (msg == "success") {
              // $('#status').html(msg)
              toastr.success("Cập nhật thành công");
              // window.location.href = "/keymanager.html";
              // var date = new Date("Februari 10, 2020");
              // var dateString = date.toGMTString();
              // var cookieString = result.split('|')[1] + dateString;

              //location.reload();
            } else {
              // $('#status').html(msg)
              toastr.error(msg);
              // $("#status").html(result);
            }
          }
        });
      }

    </script>
    <!-- Required Scripts -->
</body>

</html>