/*========== Start SVG Map  ==========*/
#svg-usa {
  height: 180px;
  background-color: transparent !important;
}

.vmap-wrapper.yoo-style1,
.vmap-wrapper.yoo-style1#svg-usa {
  height: 280px;
  background-color: transparent !important;
}

.jqvmap-label {
  padding: 2px 10px;
  background-color: $main-color;
  border-radius: 2px;
  font-size: 12px;
  color: $base-color2;
  opacity: 1;
  border: 1px solid $base-color4;
}

.jqvmap-region {
  cursor: pointer;
  stroke-opacity: 1;
  stroke: $main-color;
}

.jqvmap-zoomin,
.jqvmap-zoomout {
  border-radius: 2px;
  background-color: $base-color3;
  width: 14px;
  height: 13px;
  line-height: 7px;
  padding: 0;
  left: 0;
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.jqvmap-zoomin:hover,
.jqvmap-zoomout:hover {
  background-color: $base-color2;
}

.jqvmap-zoomout {
  top: 28px;
}

.yoo-svg-map-wrap {
  display: flex;

  .yoo-svg-map.yoo-style1 {
    width: 60%;
  }

  .yoo-progress-wrap-out {
    width: 40%;
  }

  #svg-usa {
    height: 300px;
  }

  .vmap-wrapper.yoo-style1,
  .vmap-wrapper.yoo-style1#svg-usa {
    height: 300px;
  }
}

.vmap-wrapper.yoo-style1,
.vmap-wrapper.yoo-style1#svg-usa {
  height: 285px;
  background-color: transparent !important;
}

@media screen and (max-width: 575px) {
  .yoo-svg-map-wrap {
    display: block;

    .yoo-svg-map.yoo-style1,
    .yoo-progress-wrap-out {
      width: 100%;
    }
  }
}

/*========== End SVG Map  ==========*/
