.yoo-form.yoo-style1,
.yoo-form.yoo-style2 {
  .yoo-form-title {
    font-size: 32px;
    margin-bottom: 0;
  }

  .yoo-form-subtitle {
    font-size: 16px;
    line-height: 1.6em;
  }

  .row {
    margin-right: -7px;
    margin-left: -7px;

    [class*=col-] {
      padding-left: 7px;
      padding-right: 7px;
    }
  }
}

.yoo-form-separator {
  overflow: hidden;
  display: block;
  text-align: center;
  color: $base-color3;
  position: relative;

  &:before,
  &:after {
    content: '';
    position: absolute;
    height: 1px;
    width: 50%;
    background-color: $base-color4;
    top: 9px;
  }

  &:before {
    left: -16px;
  }

  &:after {
    right: -16px;
  }
}

.yoo-form-btn.yoo-style1 {
  width: 100%;
  padding: 0px;
  height: 42px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6em;
  border-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  i {
    width: 42px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 20px;
  }

  span {
    flex: 1;
  }

  &:hover {
    opacity: 0.85;
  }
}


.yoo-form-btn.yoo-style1 {
  &.yoo-color1 {
    background-color: $blue-color;
    color: rgba(255, 255, 255, 0.9);
  }

  &.yoo-colo2 {
    background-color: $blue-color;
    color: rgba(255, 255, 255, 0.9);
  }

  &.yoo-colo3 {
    background-color: $red-color;
    color: rgba(255, 255, 255, 0.9);
  }
}

.yoo-form-btn.yoo-style2 {
  &.yoo-color1 {
    &:before {
      background-color: $base-color4;
    }

    &:after {
      background-color: $base-color2;
    }
  }
}

.yoo-form-btn.yoo-style2 {
    display: inline-block;
    position: relative;
    top: 2px;
    line-height: 1.35em;
    color: $base-color1;

  &:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: $base-color3;
  }

  &:after {
    content: "";
    width: 100%;
    height: 1px;
    position: absolute;
    bottom: 0;
    left: 0;
    transform-origin: right center;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scaleX(0);
    background-color: $blue-color;
  }

  &:hover {
    color: $blue-color;
  }
}

.yoo-form-btn.yoo-style2:hover:after {
  opacity: 1;
  transform-origin: left center;
  transform: scaleX(1);
}

.yoo-form-btn.yoo-style2.yoo-type1:before,
.yoo-form-btn.yoo-style2.yoo-type1:after {
  height: 1px;
}

.yoo-social-area.yoo-style1 {
  li:not(:last-child) {
    margin-bottom: 10px;
  }
}

.yoo-login-wrap.yoo-style1 {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 60px 0;
}

.yoo-signup-img.yoo-style1 {
  padding: 0 15px;
}

.yoo-termas {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;

  a {
    margin-left: 5px;
  }
}

.yoo-login-wrap.yoo-style1 .yoo-termas {
  margin-bottom: -3px;
  margin-top: -2px;
}

.yoo-form.yoo-style2 {
  max-width: 500px;
  width: 100%;
  background-color: #fff;
  border-radius: 10px;
  margin: auto;
  padding: 50px;
  text-align: center;
}

.yoo-login-wrap.yoo-style2 {
  display: flex;

  .yoo-left-login {
    flex: none;
    max-width: 600px;
    min-height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-bottom: 60px;

    .yoo-footer-nav {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 11px 0;
    }
  }

  .yoo-left-right {
    flex: 1;
  }
}

.yoo-login-wrap.yoo-style3 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  min-height: 100vh;
  padding: 45px 0;

  .yoo-left-login,
  .yoo-left-right {
    padding: 15px;
  }
}

.yoo-login-image {
  min-width: 460px;
  border-radius: 10px;
  display: flex;
  align-items: flex-end;
  position: relative;
  overflow: hidden;

  &.yoo-height1 {
    height: 573px;
  }

  &.yoo-height2 {
    height: 604px;
  }

  &.yoo-height3 {
    height: 620px;
  }

  .yoo-login-image-text {
    color: #fff;
    padding: 50px;
    width: 100%;
    background: linear-gradient(transparent, rgba(0, 0, 0, .8));

    p {
      font-size: 16px;
      line-height: 1.6em;
      margin-bottom: 9px;
    }

    span {
      display: block;
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

.yoo-forget-pass-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

@media screen and (max-width: 991px) {
  .yoo-login-wrap.yoo-style3 {
    flex-direction: column;
  }
}

@media screen and (max-width: 575px) {
  .yoo-login-image {
    min-width: 100%;
  }

  .yoo-login-image-text br {
    display: none;
  }

  .yoo-form-field-wrap.yoo-style1 {
    flex-direction: column;
  }

  .yoo-form-field-wrap.yoo-style1 .yoo-form-field-label {
    width: 100%;
    padding-right: 0;
    justify-content: flex-start;
    padding-left: 0;
  }
}
