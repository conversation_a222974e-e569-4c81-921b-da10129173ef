/*========== Start Live Chat  ==========*/
.yoo-live-chat-wrap {
  box-shadow: 0px 4px 36px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  width: 100%;
  max-width: 320px;
  overflow: hidden;
}

.yoo-live-chat-heading {
  background-color: $blue-color;
  border-radius: 10px 10px 0 0;
  position: relative;

  .yoo-live-chat-user {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    max-width: calc(100% - 50px);
    cursor: pointer;
  }

  .yoo-live-chat-user-img {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
  }

  .yoo-live-chat-user-name {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    margin-top: 3px;
  }

  .yoo-live-chat-user-text {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6em;
  }
}

.yoo-live-chat-list-wrap.yoo-style1 {
  .yoo-live-chat-list li {
    position: relative;
    display: flex;
    padding: 10px 20px;
    border-bottom: 1px solid $base-color4;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: $base-color5;
    }
  }

  .yoo-live-chat-list .yoo-chat-avatar {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }

  .yoo-live-chat-list .yoo-chat-time {
    position: absolute;
    right: 20px;
    top: 10px;
    font-style: normal;
    font-size: 12px;
    line-height: 1.6em;
    color: $base-color3;
  }

  .yoo-live-chat-list .yoo-conversation-user-name {
    font-size: 13px;
    margin-bottom: 1px;
    margin-top: 3px;
    font-weight: 400;
  }

  .yoo-live-chat-list .yoo-unseen-msg .yoo-conversation-user-name {
    font-weight: 500;
  }

  .yoo-live-chat-list .yoo-conversation-text p {
    font-size: 12px;
    line-height: 1.3em;
    margin-bottom: 0;
  }

  .yoo-unseen-msg {
    background-color: $base-color5;
  }

  .yoo-live-chat-list {
    margin: 0;
    padding: 0;
    list-style: none;
    overflow: auto;
    height: 340px;
    border: 1px solid $base-color4;
    border-bottom: none;
  }
}

.yoo-live-chat-list-wrap.yoo-style1,
.yoo-live-chat.yoo-style2 {
  .yoo-custom-input-area .yoo-custom-input-field {
    height: 100%;
    width: 100%;
    padding: 15px 0 15px 20px;
    font-size: 13px;
    line-height: 1.6em;
    ;
    overflow: hidden;
  }

  .yoo-conversion-input {
    border: 1px solid $base-color4;
    border-radius: 0 0 10px 10px;
    display: flex;
    align-items: flex-end;
  }

  .yoo-icon-group.yoo-style1 {
    padding: 14px 20px 14px 5px;
    ;
  }

  .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 8px;
  }
}

.yoo-live-chat.yoo-style2 {
  .yoo-conversation-list {
    margin: 0;
    padding: 0;
    list-style: none;
    overflow: auto;
    height: 340px;
    border: 1px solid $base-color4;
    border-bottom: none;
  }

  .yoo-conversation-list {
    padding: 15px 0;
  }

  .yoo-conversation-list li {
    display: flex;
    position: relative;
    align-items: flex-start;
    padding: 0px 20px;
    justify-content: flex-end;
    text-align: right;

    p {
      display: inline-block;
    }

    img {
      display: inline-block;
      margin-bottom: 5px;
    }
  }

  .yoo-conversation-list .yoo-another-side {
    justify-content: flex-start;
    background: transparent;
    text-align: left;

    p {
      background-color: $base-color4;
      color: $base-color1;
    }
  }

  .yoo-chat-avatar {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
  }

  .yoo-chat-time {
    display: none;
  }

  .yoo-conversation-text p {
    margin-bottom: 5px;
    font-size: 13px;
    line-height: 1.6em;
    background-color: $blue-color;
    padding: 5px 10px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
  }
}

.yoo-toggle-chat-btn {
  height: 48px;
  width: 48px;
  background-color: #0077fc;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 24px;
  position: relative;
  cursor: pointer;

  i,
  .hydrated {
    transition: all 0.3s ease;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    &:last-child {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0);
    }
  }
}

.yoo-toggle-chat-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 101;

  .yoo-live-chat-wrap {
    width: 320px;
    position: absolute;
    right: 0;
    bottom: 100%;
    background-color: $main-color;
    margin-bottom: -15px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
  }


  &.yoo-active {
    .yoo-live-chat-wrap {
      margin-bottom: 15px;
      opacity: 1;
      visibility: visible;
    }

    .yoo-toggle-chat-btn {

      i,
      .hydrated {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);

        &:first-child {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0);
        }

        &:last-child {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
    }
  }
}

.yoo-live-chat-heading-btn {
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  right: 15px;
  font-size: 18px;
  opacity: 0.7;
  transition: all 0.3s ease;

  &:hover {
    color: rgba(255, 255, 255, 1);
  }
}

.yoo-live-chat-list-btns {
  font-size: 24px;
  display: flex;
  justify-content: space-around;
  padding: 0px 20px;
  border-top: 1px solid $base-color4;
  color: $base-color3;

  a {
    display: flex;
    padding: 15px;
  }
}

.yoo-live-chat-body {
  position: relative;
}

.yoo-live-chat-list-wrap {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: $main-color;
  border-radius: 0 0 10px 10px;
  transition: all 0.4s ease;
}

.yoo-live-chat-wrap.yoo-active .yoo-live-chat-list-wrap {
  left: 100%;
}

.yoo-user-arrow-btn {
  color: $main-color;
  opacity: 0.7;
  display: flex;
  font-size: 0;
  margin-left: 0px;
  margin-right: 0px;
  transition: all 0.4s ease;
}

.yoo-live-chat-wrap.yoo-active {
  .yoo-user-arrow-btn {
    font-size: 18px;
    margin-left: -5px;
    margin-right: 15px;
  }
}

.yoo-chat-conversation.yoo-live-chat.yoo-style2 {
  position: relative;
  padding-bottom: 52px;

  .yoo-conversion-input {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    background: $main-color;
    overflow: auto;
  }
}

.yoo-messenger-body {
  background-color: #fff;
}

/*========== End Live Chat  ==========*/
