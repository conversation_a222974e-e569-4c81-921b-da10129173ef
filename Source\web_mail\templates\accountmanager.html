<!DOCTYPE html>
<html class="no-js" lang="en">

<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
{% include 'include/header.html' %}
    
<script src="/static/assets/editor/editor.js"></script>
<link href="/static/assets/editor/editor.css" type="text/css" rel="stylesheet" />

<body>
  <div class="yoo-height-b30 yoo-height-lg-b30"></div>
  <div class="yoo-headersidebar-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div>
  <!-- .yoo-headersidebar-toggle -->
  {% include 'include/headersidebar.html' %}

  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title">Key upload</h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Thông tin tài khoản</h2>
              </div>
            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <form>
                  <div class="form-group level-up active2">
                    <label for="exampleInputEmail18">API KEY</label>
                    <input type="email" class="form-control" id="txtapikey" aria-describedby="emailHelp">
                    <small id="emailHelp" class="form-text text-muted"></small>
                  </div>
                  <div class="form-group level-up active2">
                    <label for="exampleInputEmail18">PASSWORD</label>
                    <input type="email" class="form-control" id="txtpassword" aria-describedby="emailHelp">
                    <small id="emailHelp" class="form-text text-muted"></small>
                  </div>


                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-lg"
                   style="background-color: #04AA6D;" onclick="save(event)">Lưu lại</button>
                  <br>
                  <br>
                </form>

              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->
      {% include 'include/footer.html' %}

    </div>
    <!-- .yoo-content -->
    <script>
      document.querySelector(".yoo-uikits-title").textContent = "Tài khoản";
      GetAllApp()

      function GetAllApp() {
        $.ajax({
          url: "/accountinfo",
          type: "get",
          dataType: "text",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          success: function (results) {
            const obj = JSON.parse(results);
            $("#txtapikey").val(obj.apikey);

          }
        });
      }



      function save() {
        var person = {
          apikey: $("#txtapikey").val(),
          password: $("#txtpassword").val(),
        }
        $.ajax({
          url: "/accountedit",
          type: "post",
          dataType: "json",
          beforeSend: function (request) {
            request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
          },
          data: JSON.stringify(person),
          contentType: 'application/json',
          success: function (result) {


            var msg = result.mess;

            if (msg == "success") {
              toastr.success("Cập nhật thành công");
              // window.location.href = "/accountmanager.html";

            } else {
              toastr.error(msg);

            }
            // const obj = JSON.parse(result);
            // var result = obj.result;
            // var stringToRender = ""

            // for (let i = 0; i < result.length; i++) {
            //   const onePost = result[i];
            //   stringToRender += `${onePost.keyactive}\n`
            // }
            // $("#output").val(stringToRender);
            // CopyTextareaToClipboard('output')
          }
        });
      }

    </script>

    <!-- Required Scripts -->
</body>

</html>