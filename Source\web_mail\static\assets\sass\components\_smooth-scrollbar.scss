/*========== Start Smooth Scrollbar Style  ==========*/
scrollbar,
[scrollbar],
[data-scrollbar] {
  display: block;
  position: relative;
  overflow: scroll;
}
scrollbar .scroll-content,
[scrollbar] .scroll-content,
[data-scrollbar] .scroll-content {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  will-change: transform;
}
scrollbar.scrolling .scroll-content,
[scrollbar].scrolling .scroll-content,
[data-scrollbar].scrolling .scroll-content {
  pointer-events: none;
}
scrollbar.scrolling .scroll-content .scroll-content,
[scrollbar].scrolling .scroll-content .scroll-content,
[data-scrollbar].scrolling .scroll-content .scroll-content {
  pointer-events: auto;
}
scrollbar .scrollbar-track,
[scrollbar] .scrollbar-track,
[data-scrollbar] .scrollbar-track {
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 0.5s 1s ease-out, background 0.5s ease-out;
          transition: opacity 0.5s 1s ease-out, background 0.5s ease-out;
  background: none;
}
scrollbar .scrollbar-track.show,
[scrollbar] .scrollbar-track.show,
[data-scrollbar] .scrollbar-track.show,
scrollbar .scrollbar-track:hover,
[scrollbar] .scrollbar-track:hover,
[data-scrollbar] .scrollbar-track:hover {
  opacity: 1;
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}
scrollbar .scrollbar-track:hover,
[scrollbar] .scrollbar-track:hover,
[data-scrollbar] .scrollbar-track:hover {
  background: transparent;
}
scrollbar .scrollbar-track-x,
[scrollbar] .scrollbar-track-x,
[data-scrollbar] .scrollbar-track-x {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
}
scrollbar .scrollbar-track-y,
[scrollbar] .scrollbar-track-y,
[data-scrollbar] .scrollbar-track-y {
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
}
scrollbar .scrollbar-thumb,
[scrollbar] .scrollbar-thumb,
[data-scrollbar] .scrollbar-thumb {
  position: absolute;
  top: 0;
  left: -5px;
  width: 6px;
  height: 8px;
  background: transparent;
  border-radius: 4px;
}
[data-scrollbar] .scrollbar-thumb:before,
[data-scrollbar] .scrollbar-thumb:after {
    content: '';
    position: absolute;
    height: calc(100% - 40px);
    width: 6px;
    background: $base-color4;
    top: 20px;
    right: 0;
    border-radius: 4px;
}
[data-scrollbar] .scrollbar-thumb:before {
  z-index: 1;
}
[data-scrollbar] .scrollbar-thumb:after {
  background-color: $main-color;
}
/*========== End Smooth Scrollbar Style  ==========*/
