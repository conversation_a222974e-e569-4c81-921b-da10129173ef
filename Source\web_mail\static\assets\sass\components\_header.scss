/*========== Start Header  ==========*/
.yoo-side-heading {
  height: 60px;
  background-color: $main-color;
  border-bottom: 1px solid $base-color4;
  position: relative;
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
}

.yoo-header {
  position: relative;
  z-index: 101;
}

.yoo-header.yoo-sticky-menu {
  position: fixed;
  width: 100%;
  top: 0;
  background: #212121;
  z-index: 100;
}

.yoo-header.yoo-style1 .yoo-main-header {
  position: relative;
}

.yoo-header.yoo-style1 .yoo-main-header-in {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.yoo-header.yoo-style2 {
  border-top: 1px solid $base-color4;
  border-bottom: 1px solid $base-color4;
}

/*Style2*/
.yoo-header.yoo-style1 .yoo-main-header {
  position: relative;
}

.yoo-header.yoo-style2 .yoo-main-header-in {
  height: 60px;
  display: flex;
  align-items: center;
}

/*=== Logo Area ===*/
.yoo-site-title {
  font-size: 20px;
  font-weight: 700;
  color: $base-color1;
}

.yoo-site-title a {
  color: inherit;
}

.yoo-logo-link img {
  margin-left:30px;
  display: block;
  height: 32px;
}

/*End Logo Area*/

/*Horizontal Menu Area*/
.yoo-nav-wrap {
  display: flex;
  align-items: center;
}

.yoo-nav-wrap,
.yoo-desktop-nav.yoo-nav,
.yoo-desktop-nav .yoo-nav-list {
  height: 100%;
}

.yoo-nav-list>li>a>i,
.yoo-nav-list>li>a>.hydrated {
  color: rgba(255, 255, 255, 0.54);
  font-size: 24px;
  margin-right: 8px;
}

.yoo-nav.yoo-desktop-nav {
  display: block !important;
}

.yoo-desktop-nav .yoo-nav-list {
  margin: 0;
  display: flex;
  align-items: center;
  padding: 0;
  flex-wrap: wrap;
  list-style: none;
}

.yoo-desktop-nav .yoo-nav-list>li {
  margin-left: 35px;
  display: flex;
  align-items: center;
}

.yoo-desktop-nav .yoo-nav-list>li:first-child {
  margin-left: 0;
}

.yoo-desktop-nav .yoo-nav-list>li>a {
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0px;
  border-radius: 4px 4px 0 0;
}

.yoo-desktop-nav .yoo-nav-list>li:hover>a {
  background-color: $base-color6;
}

.yoo-nav-list>li>a {
  color: rgba(255, 255, 255, 0.87);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
}

.yoo-nav-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 9px;
  font-weight: 500;
  line-height: 1.7em;
  padding: 0 4px;
  border-radius: 2px;
  display: inline-block;
}

.yoo-nav-label.yoo-style1 {
  position: relative;
  top: -6px;
  margin-left: 5px;
}

.yoo-nav-label.yoo-color1 {
  background: $green-color;
}

.yoo-nav-label.yoo-color2 {
  background: $light-blue-color;
}

.yoo-nav-label.yoo-style2 {
  background-color: $light-blue-color;
  margin-left: auto;
}

/*=== Horizontal Dropdown ===*/
.yoo-has-children {
  position: relative;
}

.yoo-desktop-nav .yoo-nav-list>.yoo-has-children>a {
  position: relative;
}

.yoo-desktop-nav .yoo-nav-list>li>a.active {
  color: $base-color3;
}

.yoo-desktop-nav .yoo-nav-list>.yoo-has-children>a:before {
  border-left: 0;
  border-top: 0;
}

.yoo-desktop-nav .yoo-has-children .yoo-has-children>a:before {
  border-left: 0;
  border-bottom: 0;
  right: 18px;
  transform: rotate(-90deg);
}

.yoo-nav .yoo-dropdown {
  border-radius: 4px;
}

.yoo-nav-list ul a,
.yoo-profile-nav .yoo-dropdown a {
  color: $base-color1;
  padding: 9px 20px;
  display: block;
  font-size: 14px;
}

.yoo-desktop-nav .yoo-nav-list .yoo-sub-megamenu-list li a:hover {
  background: transparent;
}

.yoo-desktop-nav .yoo-nav-list ul a:hover,
.yoo-desktop-nav .yoo-nav-list .yoo-sub-megamenu-list li.active a,
.yoo-profile-nav .yoo-dropdown a:hover {
  background-color: $base-color5;
}

.yoo-desktop-nav .yoo-nav-list .yoo-has-children>ul ul {
  left: 100%;
  top: 0;
}

.yoo-desktop-nav .yoo-has-children:hover>ul {
  opacity: 1;
  visibility: visible;
  display: block !important;
}

.yoo-nav-list ul .yoo-has-children>a {
  padding-right: 25px;
}

.yoo-dropdown.yoo-style1 li a {
  display: flex;
  align-items: center;
}

.yoo-dropdown.yoo-style1 li i,
.yoo-dropdown.yoo-style1 li .hydrated {
  font-size: 24px;
  color: $base-color3;
  flex: none;
  margin-right: 8px;
}

/*Style 2,3,4*/
.yoo-dropdown.yoo-style2 li,
.yoo-dropdown.yoo-style3 li,
.yoo-dropdown.yoo-style4 li,
.yoo-dropdown.yoo-style5 li {
  padding: 0 10px;
}

.yoo-dropdown.yoo-style2 a,
.yoo-dropdown.yoo-style3 a,
.yoo-dropdown.yoo-style4 a {
  padding: 7px 10px;
  border-radius: 2px;
  display: flex;
  align-items: center;
}

.yoo-dropdown.yoo-style3 .yoo-dropdown-cta a:hover,
.yoo-dropdown.yoo-style4 .yoo-dropdown-cta a:hover {
  padding-left: 10px;
}

.yoo-dropdown.yoo-style2 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style3 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style4 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style5 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style6 .yoo-subdropdown-title,
.yoo-dropdown.yoo-style7 .yoo-subdropdown-title {
  font-size: 16px;
  color: $base-color1;
  font-weight: 500;
  padding: 7px 20px 5px !important;
}

.yoo-dropdown.yoo-style1 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style2 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style3 .yoo-dropdown-cta,
.yoo-dropdown.yoo-style4 .yoo-dropdown-cta {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid $base-color4;
}

.yoo-dropdown.yoo-style2 li i,
.yoo-dropdown.yoo-style3 li i {
  display: inline-block;
  margin-right: 8px;
  width: 13px;
  transition: all 0.35s ease;
}

.yoo-dropdown.yoo-style4 li a:hover i {
  width: 9px;
  margin-right: 4px;
}

/*Style 5,6,7*/
.yoo-desc-title {
  font-size: 14px;
  line-height: 1.25em;
  margin-bottom: 2px;
  color: $base-color1;
}

.yoo-desc-cat {
  display: block;
  color: $base-color3;
  line-height: 1.25em;
  font-size: 13px;
}

.yoo-dropdown.yoo-style5 a,
.yoo-dropdown.yoo-style6 a,
.yoo-dropdown.yoo-style7 a {
  display: flex;
  align-items: center;
}

.yoo-dropdown.yoo-style5 a {
  padding: 9px 10px;
}

.yoo-dropdown.yoo-style6 a,
.yoo-dropdown.yoo-style7 a {
  padding: 10px 20px;
}

.yoo-desc-box {
  flex: none;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  font-size: 18px;
}

.yoo-desc-box i {
  font-size: 21px;
}

.yoo-desc-box img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/*=== Hovizontal Mega Menu ===*/
.yoo-desktop-nav .yoo-has-children.yoo-megamenu.yoo-style1 {
  position: initial;
}

.yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 100%;
  width: 1170px;
  background-color: $main-color;
  border-radius: 4px;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  opacity: 0;
  visibility: hidden;
  z-index: 2;
  margin-left: 0px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  opacity: 1;
  visibility: visible;
}

.yoo-megamenu-title {
  font-size: 12px;
  line-height: 1.65em;
  margin-bottom: 0;
  text-transform: uppercase;
  color: $base-color3
}

.yoo-desktop-nav .yoo-megamenu-title {
  padding: 6px 20px;
}

.yoo-megamenu-list {
  padding: 0;
  list-style: none;
}

.yoo-megamenu.yoo-style1 .row {
  margin: 0;
}

.yoo-megamenu.yoo-style1 .container,
.yoo-megamenu.yoo-style1 .row>div {
  padding: 0;
  margin-top: 0;
}

.yoo-desktop-nav .yoo-nav-list .yoo-megamenu ul a {
  padding: 9px 20px;

  &:hover {
    background-color: $base-color5;
  }
}

.yoo-desktop-nav .yoo-megamenu-col {
  height: 100%;
  padding: 10px 0;
  border-right: 1px solid $base-color4;
}

.yoo-desktop-nav .row>div:last-child>.yoo-megamenu-col {
  border-right: 0;
}

/*Style2 3 4*/
.yoo-megamenu.yoo-style2 .container {
  padding-left: 30px;
  padding-right: 30px;
}

.yoo-megamenu.yoo-style2 .yoo-megamenu-in {
  padding-top: 15px;
  padding-bottom: 15px;
}

.yoo-megamenu.yoo-style3 .row,
.yoo-megamenu.yoo-style4 .row {
  margin-top: -30px;
}

.yoo-megamenu.yoo-style2 .row>div {
  padding-top: 15px;
  padding-bottom: 15px;
}

.yoo-megamenu.yoo-style3 .row>div,
.yoo-megamenu.yoo-style4 .row>div {
  margin-top: 30px;
}

/*Style5*/
.yoo-megamenu.yoo-style5 .row {
  position: relative;
}

.yoo-megamenu.yoo-style5 .row:before {
  content: "";
  height: 1px;
  width: calc(100% + 30px);
  position: absolute;
  background: $main-color;
  bottom: 0px;
  left: -15px;
  z-index: 1;
}

.yoo-megamenu.yoo-style5 .row>div {
  padding-top: 30px;
  padding-bottom: 30px;
  position: relative;
}

.yoo-megamenu.yoo-style5 .row>div:before {
  content: "";
  position: absolute;
  height: 1px;
  width: calc(100% + 30px);
  background-color: $base-color4;
  bottom: 0;
  left: -15px;
}

/*Style 3, 4*/
.yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
.yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
  list-style: none;
  padding: 30px 0;
  width: 230px;
  flex: none;
  border-right: 1px solid $base-color4;
}

.yoo-megamenu.yoo-style3 .yoo-megamenu-in {
  display: flex;
}

.yoo-megamenu-in {
  overflow: hidden;
}

.yoo-megamenu.yoo-style3 .yoo-sub-megamenu,
.yoo-megamenu.yoo-style4 .yoo-sub-megamenu,
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu {
  width: 100%;
  padding: 30px 15px;
}

.yoo-megamenu.yoo-style6 .yoo-megamenu-in {
  padding: 30px 15px;
}

/*Style5*/
.yoo-megamenu.yoo-style5 .yoo-sub-megamenu {
  padding-top: 0;
  padding-bottom: 0;
}

/*End Horizontal Mega menu*/

/*=== Horizontal Mobile Menu Style ===*/

.yoo-mobile-nav {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  background-color: #212121;
  padding: 20px 0;
}

.yoo-mobile-nav ul {
  margin: 0;
  list-style: none;
  padding: 0;
  padding-left: 15px;
}

.yoo-mobile-nav .yoo-nav-list ul {
  display: none;
}

.yoo-mobile-nav .yoo-nav-list>li>a {
  padding: 8px 0px;
  display: flex;
}

.yoo-mobile-nav .yoo-nav-list ul a {
  padding-left: 0;
  padding-right: 0;
}

.yoo-sub-megamenu-in {
  display: none;
}

.yoo-sub-megamenu-in.active {
  display: block;
}

.yoo-nav.yoo-mobile-nav {
  max-height: calc(100vh - 60px);
  overflow: auto;
}

.yoo-mobile-nav .yoo-nav-list .yoo-megamenu-list {
  display: block;
}

.yoo-mobile-nav .yoo-megamenu-title {
  padding: 7px 15px;
}

.yoo-mobile-nav .yoo-nav-list .yoo-megamenu-list a {
  padding-left: 10px;
  padding-right: 10px;
}

.yoo-mobile-nav .yoo-megamenu-in {
  display: none;
}

.yoo-mobile-nav .yoo-dropdown-btn {
  height: 20px;
  width: 20px;
  position: absolute;
  right: 15px;
  top: 9px;
  cursor: pointer;
  z-index: 1;
}

.yoo-mobile-nav .yoo-dropdown-btn:before,
.yoo-mobile-nav .yoo-dropdown-btn:after {
  content: "";
  position: absolute;
  height: 2px;
  width: 8px;
  background-color: rgba(255, 255, 255, 0.38);
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.yoo-mobile-nav .yoo-dropdown-btn:before {
  -webkit-transform: translate(-50%, -50%) rotate(90deg);
  -ms-transform: translate(-50%, -50%) rotate(90deg);
  transform: translate(-50%, -50%) rotate(90deg);
}

.yoo-mobile-nav .yoo-dropdown-btn.yoo-active:before {
  transform: translate(-50%, -50%) rotate(0deg);
}

/*End Horizontal Mobile Menu*/

/*=== Mobile Menu Button ===*/

.yoo-nav-toggle {
  position: relative;
  cursor: pointer;
  font-size: 23px;
  display: flex;
  color: rgba(255, 255, 255, 0.38);
  margin-left: -9px;
  display: none;
  padding: 0 10px;

  &.yoo-active {
    color: rgba(255, 255, 255, 0.6);
  }
}

/*Ene Mobile Menu Button*/

/*=== Horizontal Menu Animation Efect ===*/
.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-has-children>ul {
  margin-top: 15px;
}

.yoo-fade-up .yoo-desktop-nav .yoo-has-children:hover>ul {
  margin-top: 0px;
}

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 ul {
  margin-left: 0;
  padding: 10px 0;
  list-style: none;
  position: absolute;
  background-color: $main-color;
  width: 100%;
  left: 100%;
  top: 0;
  margin-top: 15px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border: 1px solid $base-color4;
}

.yoo-fade-up .yoo-desktop-nav .yoo-nav-list .yoo-dropdown.yoo-style1 .yoo-has-children:hover>ul {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}

.yoo-fade-up .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  margin-top: 15px;
}

.yoo-fade-up .yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  margin-top: -10px;
}

.yoo-fade-down .yoo-desktop-nav .yoo-nav-list .yoo-has-children>ul {
  margin-top: -20px;
  transition: all 0.3s ease;
}

.yoo-fade-down .yoo-desktop-nav .yoo-has-children:hover>ul {
  margin-top: -10px;
}

.yoo-fade-down .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
  margin-top: -20px;
  transition: all 0.3s ease;
}

.yoo-fade-down .yoo-desktop-nav .yoo-has-children:hover .yoo-megamenu-in {
  margin-top: -10px;
}

/*Ene Horizontal Menu Animation Effect*/

/*=== Menu Responsive Style ===*/
@media screen and (min-width: 991px) {
  .container {
    max-width: 1200px;
    width: 100%;
  }
}

@media screen and (max-width: 991px) {

  .yoo-nav-toggle,
  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
    display: block;
  }

  .yoo-nav.yoo-mobile-nav,
  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu,
  .yoo-megamenu.yoo-style3 .yoo-megamenu-in {
    display: none;
  }

  .yoo-header .container {
    max-width: 100%;
  }

  .yoo-megamenu.yoo-style3 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style4 .yoo-sub-megamenu-list,
  .yoo-megamenu.yoo-style5 .yoo-sub-megamenu-list {
    width: 100%;
    padding: 0px 15px;
  }

  .yoo-megamenu.yoo-style2 .yoo-megamenu-in {
    padding: 0;
  }

  .yoo-dropdown.yoo-style2 li,
  .yoo-dropdown.yoo-style3 li,
  .yoo-dropdown.yoo-style4 li,
  .yoo-dropdown.yoo-style5 li,
  .yoo-dropdown.yoo-style6 li,
  .yoo-dropdown.yoo-style7 li {
    padding-left: 0;
    padding-right: 0px;
  }

  .yoo-dropdown.yoo-style2 a,
  .yoo-dropdown.yoo-style3 a,
  .yoo-dropdown.yoo-style4 a {
    padding-left: 0;
    padding-right: 0;
  }

  .yoo-dropdown.yoo-style2 .yoo-dropdown-cta,
  .yoo-dropdown.yoo-style3 .yoo-dropdown-cta,
  .yoo-dropdown.yoo-style4 .yoo-dropdown-cta {
    margin-top: 0;
    padding-top: 0;
    border: none;
  }

  .yoo-dropdown.yoo-style2 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style3 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style4 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style5 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style6 .yoo-subdropdown-title,
  .yoo-dropdown.yoo-style7 .yoo-subdropdown-title {
    padding-left: 0 !important;
  }

  .yoo-dropdown.yoo-style5 a,
  .yoo-dropdown.yoo-style6 a,
  .yoo-dropdown.yoo-style7 a {
    padding-left: 0;
    padding-right: 0;
  }

  .yoo-mobile-nav {
    border-bottom: 1px solid $base-color4;
  }

  .yoo-megamenu.yoo-style2 .container {
    padding-left: 15px;
  }

  .yoo-megamenu.yoo-style6 .yoo-megamenu-in {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .yoo-header.yoo-style2 .yoo-main-header-in {
    justify-content: space-between;
  }

  .yoo-nav .yoo-has-children .yoo-dropdown {
    position: initial;
    opacity: initial;
    visibility: visible;
    width: calc(100% - 15px);
    display: none;
    transition: initial;
  }

  .yoo-nav-wrap .yoo-nav-toggle {
    padding: 0;
    display: flex;
  }
}

/*End Menu Responsive*/

/*=== Vertical Menu Style ===*/
.yoo-vertical-nav-wrap {
  height: 100%;
  width: 240px;
  margin-right: 30px;
}

.yoo-vertical-nav-btn {
  height: 60px;
  display: flex;
  align-items: center;
  background-color: $blue-color;
  color: $main-color;
  padding: 10px 20px;
  border-radius: 4px 4px 0 0;
  width: 100%;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.yoo-vertical-nav-btn i {
  margin-right: 8px;
}

.yoo-vertical-nav.yoo-style1 {
  width: 240px;
}

.yoo-vertical-nav-list {
  padding: 0;
  margin: 0;
  list-style: none;
  background-color: $main-color;
  position: relative;
  width: 100%;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
}

.yoo-desktop-nav .yoo-vertical-nav-btn.yoo-vertical-nav-perform {
  pointer-events: none;
}

.yoo-vertical-nav-list li a {
  font-size: 15px;
  color: $base-color2;
  display: flex;
  align-items: center;
  padding: 8px 20px;
  width: 100%;
  line-height: 1.65em;
}

.yoo-vertical-nav-list .yoo-vertical-cta>a {
  font-size: 12px;
  font-weight: 700;
  padding: 11px 20px;
  line-height: 1.5em;
}

.yoo-vertical-nav-list>li {
  border-bottom: 1px solid $base-color4;
}

.yoo-vertical-nav-list>li:last-child {
  border-bottom: none;
}

.yoo-vertical-nav-list li a:hover {
  background-color: $base-color6;
}

.yoo-vertical-nav-list>li>a>i {
  margin-right: 10px;
}

.yoo-desktop-nav .yoo-has-children.yoo-vertical-megamenu {
  position: initial;
}

.yoo-vertical-arrow {
  display: inline-block;
  background-image: url(../img/arrow.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 8px;
  width: 8px;
  transform: rotate(-90deg);
  margin-left: 7px;
  flex: none;
}

.yoo-desktop-nav .yoo-vertical-megamenu-in {
  position: absolute;
  left: 100%;
  margin-left: 0;
  width: calc(1170px - 240px);
  min-height: 100%;
  background: $main-color;
  top: 0;
  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  border-left: 1px solid $base-color4;
  border-top: 1px solid $base-color4;
}

.yoo-has-children:hover .yoo-vertical-megamenu-in {
  opacity: 1;
  visibility: visible;
}

.yoo-vertical-megamenu-in .container {
  padding: 0;
}

.yoo-vertical-megamenu-in .row {
  margin: 0;
}

.yoo-vertical-megamenu-in .row>div {
  padding: 0;
  border-top: 1px solid $base-color4;
  margin-top: -1px;
}

.yoo-desktop-nav.yoo-vertical-nav-wrap .yoo-megamenu-col {
  border-right: none;
}

/*Vertical Mobile menu*/
.yoo-vertical-nav-wrap.yoo-mobile-nav {
  position: relative;
  top: 0;
  padding: 0;
}

.yoo-mobile-nav .yoo-has-children .yoo-vertical-arrow {
  display: none;
}

.yoo-mobile-nav .yoo-vertical-nav-list {
  padding-left: 0;
  display: block !important;
  height: initial !important;
}

.yoo-mobile-nav .yoo-vertical-nav-list li a {
  padding-left: 15px;
  padding-right: 15px;
}

.yoo-mobile-nav .yoo-vertical-nav.yoo-style1 {
  max-height: calc(100vh - 60px);
  overflow: auto;
  border: 1px solid $base-color4;
  border-radius: 4px;
  width: 240px;
  margin-left: -300px;
  transition: all 0.4s ease;
}

.yoo-mobile-nav .yoo-mobile-active+.yoo-vertical-nav.yoo-style1 {
  margin-left: 0;
}

.yoo-mobile-nav .yoo-vertical-nav-list {
  width: 100%;
}

.yoo-mobile-nav .yoo-vertical-megamenu-in {
  display: none;
}

.yoo-mobile-nav .yoo-vertical-megamenu-in .row>div {
  margin-top: 0;
}

.yoo-mobile-nav .yoo-megamenu-col {
  padding: 10px 0;
}

/*End Vertical Menu Style*/
/*=== Vertical Menu Animation Efect ===*/
.yoo-fade-left.yoo-desktop-nav .yoo-vertical-megamenu-in {
  margin-left: -30px;
  transition: all 0.4s ease;
}

.yoo-fade-left.yoo-desktop-nav .yoo-has-children:hover .yoo-vertical-megamenu-in {
  margin-left: 0px;
}

.yoo-fade-left.yoo-desktop-nav .yoo-has-children .yoo-vertical-megamenu-in {
  display: block !important;
}

/*End Vertical Menu Animation Efect*/

// New CSS
.yoo-main-header-left {
  width: 300px;
  border-right: 2px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 15px;
  transition: all 0.3s ease;
}

.yoo-main-header-right {
  padding: 0 30px;
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: space-between;
}

// Extra Menu
.yoo-profile-nav.yoo-style1 {
  padding-left: 20px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);

  .yoo-profile-nav-btn {
    display: flex;
    cursor: pointer;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .yoo-profile-nav-text {
    text-align: right;
    margin-right: 8px;

    span {
      color: rgba(255, 255, 255, 0.38);
      font-size: 12px;
      display: block;
      line-height: 1.3em;
    }

    h4 {
      margin-bottom: 0;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 500;
    }
  }

  .yoo-profile-nav-img {
    flex: none;
    border-radius: 50%;
    overflow: hidden;
    height: 32px;
    width: 32px;
  }
}

.yoo-ex-nav-btn {
  display: block;
  color: rgba(255, 255, 255, 0.54);
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  padding: 8px 0;
  border-radius: 4px;
  transition: all 0.4s ease;
}

.yoo-toggle-body:hover .yoo-ex-nav-btn {
  color: #fff;
}

.yoo-toggle-body .yoo-ex-nav-btn.active {
  border-radius: 4px 4px 0 0;
}

.yoo-ex-nav-label {
  position: absolute;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.9);
  background-color: $red-color;
  line-height: 1.4em;
  padding: 0 4px;
  border-radius: 50%;
  top: 5px;
  right: 0px;
  transform: translateX(40%);
  z-index: 2;
  min-width: 15px;
}

.yoo-ex-nav.yoo-style1>li:not(:first-child) {
  margin-left: 20px;
}


.yoo-notice-area.yoo-style1 .yoo-ex-nav-label {
  transform: translateX(20%);
}

.yoo-toggle-body {
  position: relative;

  .yoo-dropdown.yoo-style1 {
    left: initial;
    right: 0;
    margin-top: 10px;

    &.active {
      visibility: visible;
      opacity: 1;
      margin-top: 0;
    }
  }

  .yoo-dropdown.yoo-notify-dropdown {
    left: initial;
    right: 0;
    margin-top: 10px;
    width: 400px;
    padding: 0;

    &.active {
      visibility: visible;
      opacity: 1;
      margin-top: 0;
    }
  }

  &.yoo-start-left {
    .yoo-dropdown.yoo-notify-dropdown {
      right: inherit;
      left: 0;
    }
  }
}

// Notify List
.yoo-notify-dropdown {
  .yoo-btn.yoo-style2 {
    padding: 8px 15px 7px;
    background-color: $base-color5;

    &:hover {
      background-color: $base-color5;
    }
  }
}

.yoo-msg {
  line-height: 1.45em;
  margin-top: 2px;
  font-size: 13px;
  color: $base-color3;
}

.yoo-nofify-list.yoo-style1 {
  background: $main-color;

  li {

    >a,
    .yoo-nofify-list-in {
      display: flex;
      padding: 13px 15px;
      position: relative;
      transition: all 0.3s ease;

      &.active {
        background-color: $base-color6;
      }

      &:hover {
        background-color: $base-color5;
        color: $base-color2;
      }
    }

    border-bottom: 1px solid $base-color4;

    .yoo-nofify-list-in {
      .yoo-nofify-text-head {
        color: $base-color1;

        a {
          color: $base-color1;

          &:hover {
            color: rgba($base-color1, 0.9);
          }
        }
      }
    }
  }

  .yoo-nofify-thumb {
    height: 48px;
    width: 48px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 10px;
    flex: none;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }

  .yoo-nofify-text {
    flex: 1;
    position: relative;

    .yoo-nofify-text-head {
      font-size: 14px;
      margin-bottom: 3px;
      font-weight: 400;
      line-height: 1.2em;
    }

    .yoo-notify-time {
      color: $base-color3;
      line-height: 1em;
      font-size: 13px;
    }

    strong {
      font-weight: 600;
    }

    span {
      color: $base-color2;
    }

    .yoo-msg+.yoo-notify-time {
      font-size: 12px;
    }
  }

  .yoo-online-status {
    margin-left: 4px;
    margin-bottom: 2px;
  }
}

.yoo-message-area .yoo-nofify-list.yoo-style1 .yoo-nofify-text {
  .yoo-notify-time {
    position: absolute;
    top: 2px;
    right: 0;
  }
}

.yoo-nofify-list.yoo-style2 {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 5px 0;

  li {
    width: 33.333333%;
    margin-bottom: 10px;
    padding: 0px 5px;
  }

  a {
    display: block;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 20px 0;

    &:hover {
      border-color: $base-color4;
    }
  }

  .yoo-nofify-icon {
    margin: auto;
    margin-top: 5px;
  }

  .yoo-nofify-text {
    h3 {
      text-align: center;
      font-size: 13px;
      color: $base-color2;
      font-weight: 400;
      margin-bottom: 3px;
      margin-top: 9px;
    }
  }

  .yoo-nav-icon {
    text-align: center;
    max-width: 60px;
    margin: auto;
    border-radius: 10px;
    overflow: hidden;
  }
}

.yoo-nofify-title {
  font-size: 14px;
  margin-bottom: 0;
  padding: 12px 15px;
  border-bottom: 1px solid $base-color4;
  display: flex;
  justify-content: space-between;
  font-weight: 500;

  .yoo-nofify-title-right {
    font-size: 14px;
    font-weight: 500;
    color: $base-color3;
  }

  a {
    &:hover {
      color: $base-color2;
    }
  }
}

.yoo-nofify-icon {
  flex: none;
  height: 48px;
  width: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 21px;
  overflow: hidden;

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}


/* Start Search Area */
.yoo-dropdown.active .yoo-search-input {
  border-radius: 4px;
}

.yoo-search-area.yoo-toggle-body .yoo-dropdown {
  border: none;
  box-shadow: none;
  width: 120px;
  padding: 0;
  top: 5px;
  left: initial;
  right: 0;
}

.yoo-search-area.yoo-toggle-body.yoo-start-left .yoo-dropdown {
  right: inherit;
  left: 0;
}

.yoo-search-area.yoo-toggle-body .yoo-dropdown.active {
  opacity: 1;
  visibility: visible;
  width: 250px;
}

.yoo-search-area.yoo-toggle-body .yoo-toggle-btn.active {
  visibility: hidden;
}

/* End Search Area */

.yoo-header.yoo-sticky-header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
}

.yoo-header.yoo-style3 {
  padding: 30px 40px;
  padding-right: 70px;
  transition: all 0.3s ease;

  .yoo-main-header-in {
    display: flex;
    justify-content: space-between;
    position: relative;
  }

  .yoo-main-header-left {
    border: none;
  }

  .yoo-main-header-right {
    flex: none;
    padding: 0;
  }

  .yoo-nav-list>li>a {
    color: $base-color2;
    font-size: 14px;
    padding: 13px 10px;
  }

  .yoo-desktop-nav .yoo-has-children .yoo-megamenu-in {
    left: initial;
    right: 0;
  }

  .yoo-has-children.yoo-megamenu {
    position: initial;
  }

  .yoo-desktop-nav .yoo-nav-list>li:hover>a {
    background-color: transparent;
    color: $base-color1;
  }
}

.yoo-header.yoo-style3.yoo-sticky-header.yoo-sticky-active {
  padding-top: 10px;
  padding-bottom: 10px;
  background: #fff;
  border-bottom: 1px solid #eaeaea;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
  background-color: $blue-color;
}

.nav-item a,
.nav-pills .nav-link {
  color: $base-color2;
  letter-spacing: 0.17px;
}

.nav-item a.dropdown-item {
  color: $base-color2;
}

.nav-item a.dropdown-item.active {
  color: $blue-color;
  background-color: rgba($blue-color, 0.1);
}

.navbar-light .navbar-brand {
  color: $base-color1;
  font-weight: 500;
}

.navbar.flex-column .nav {
  width: 100%;
}

.navbar.flex-column {
  padding: 10px;
}

.navbar.flex-column .nav {
  width: 100%;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
  background-color: rgba($blue-color, 0.1);
  color: $blue-color;
}

.nav-item .nav-link:hover {
  color: $blue-color;
}

.nav-link.disabled {
  color: $base-color3;
}

.nav-item .nav-link.active {
  color: $blue-color;
}

.nav-tabs,
.nav-pills {
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f7;
  border-radius: 5px;
  padding: 3px;
  border: none;
  display: inline-flex;

  .nav-item {
    text-align: center;
    display: flex;
    position: relative;
    padding: 0;
    margin: 0;
  }

  .nav-link {
    border: none;
    line-height: 1.6em;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 7px 30px;
    border-radius: 5px;
    color: $base-color1;
    position: relative;
    z-index: 1;
    font-weight: 400;

    &:hover {
      opacity: 0.6;
      color: $base-color1;
    }

    &.active {
      color: $base-color1;
      font-weight: 500;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.nav-pills .nav-link.active {
  background-color: $blue-color;
  color: #fff;
}

.nav-fill,
.nav.flex-column {
  width: 100%;

  .nav-link {
    width: 100%;
  }
}

.nav.flex-column {
  padding: 3px;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  background-color: #fff;
  border: none;
}

.yoo-card.yoo-style1 .nav-tabs .nav-item:not(:last-child) {
  margin-right: 1px;
}

.nav-tabs .nav-item .nav-link.active {
  z-index: 2;
  box-shadow: 0px 2px 11.52px 0.48px rgba(0, 0, 0, 0.1);
}

.nav-tabs .nav-item .nav-link:before,
.nav-tabs .nav-item .nav-link:after {
  content: '';
  position: absolute;
  height: 20px;
  width: 1px;
  background-color: rgba($base-color, 0.1);
  top: 50%;
  transform: translateY(-50%);
}

.nav-tabs .nav-item .nav-link.active:before,
.nav-tabs .nav-item .nav-link.active:after {
  background-color: #f0f0f7;
}

.nav-tabs .nav-item .nav-link:before {
  right: -1px;
}

.nav-tabs .nav-item .nav-link:after {
  left: -1px;
}

.nav-tabs .nav-item:last-child .nav-link:before {
  display: none;
}

.nav-tabs .nav-item:first-child .nav-link:after {
  display: none;
}

.yoo-card.yoo-style1 {

  .nav-tabs .nav-item .nav-link:before,
  .nav-tabs .nav-item .nav-link:after {
    height: 15px;
  }
}

/* Header Responsive */

@media screen and (max-width: 1199px) {
  .yoo-mobile-nav .yoo-megamenu-title {
    color: rgba(255, 255, 255, 0.38);
  }
}

@media screen and (max-width: 991px) {
  .yoo-main-header-right {
    padding: 0 20px;
  }

  .yoo-desktop-nav .yoo-nav-list>li {
    margin-left: 18px;
  }

  .yoo-nav-list ul a,
  .yoo-profile-nav .yoo-dropdown a {
    color: rgba(255, 255, 255, 0.6);
  }
}

@media screen and (max-width: 767px) {
  .yoo-main-header-left {
    width: initial;
    padding-left: 58px;
    border-right-width: 1px;
  }

  .yoo-logo-link img {
    margin-left:30px;
    height: 28px;
  }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-text {
    display: none;
  }

  .yoo-profile-nav.yoo-style1 {
    margin-left: 0px;
    padding-left: 15px;
  }

  .yoo-main-header .yoo-toggle-body {
    position: initial;
  }

  .yoo-main-header .yoo-toggle-body .yoo-dropdown.yoo-notify-dropdown {
    right: initial;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
  }

  .yoo-mobile-nav .yoo-megamenu-col {
    padding: 0;
  }

  .yoo-main-header .yoo-toggle-body.yoo-search-area {
    position: relative;
  }
}


@media screen and (max-width: 575px) {
  .yoo-ex-nav.yoo-style1>li:not(:first-child) {
    margin-left: 15px;
  }
}

@media screen and (max-width: 485px) {
  .yoo-logo-link img {
    margin-left:30px;
    height: 22px;
  }

  .yoo-ex-nav-btn {
    font-size: 22px;
  }

  .yoo-ex-nav.yoo-style1>li:not(:first-child) {
    margin-left: 12px;
  }

  .yoo-main-header .yoo-toggle-body {
    border: none;
    padding-left: 0;
  }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-img {
    height: 28px;
    width: 28px;
  }
}

@media screen and (max-width: 410px) {
  .yoo-main-header-left {
    padding-left: 53px;
  }

  .yoo-main-header-right {
    padding-left: 15px;
  }

  .yoo-logo-link img {
    margin-left:30px;
    height: 20px;
  }

  .yoo-nav-toggle,
  .yoo-ex-nav-btn {
    font-size: 20px;
  }

  .yoo-ex-nav.yoo-style1>li:not(:first-child) {
    margin-left: 8px;
  }

  .yoo-profile-nav.yoo-style1 .yoo-profile-nav-img {
    height: 22px;
    width: 22px;
  }

  .yoo-search-area.yoo-toggle-body .yoo-dropdown.active {
    width: 180px;
  }
}

@media screen and (max-width: 360px) {
  .yoo-sidebarheader-toggle {
    left: 10px;
  }

  .yoo-main-header-right {
    padding-right: 10px;
    padding-left: 0;
  }

  .yoo-main-header-left {
    padding-left: 40px;
    border-right: none;
  }
}


/*========== End Header  ==========*/
