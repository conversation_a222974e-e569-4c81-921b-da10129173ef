/*========== Start Email  ==========*/
.yoo-outline-label {
  font-size: 24px;
  display: flex;
  height: 100%;
  color: $base-color3;
}

.yoo-email-badgh {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  background-color: $green-color;
  border-radius: 4px;
  padding: 2px 6px;
}

.yoo-email-text.yoo-style1 {
  font-size: 16px;
  display: flex;
  max-width: calc(100% - 50px);

  .yoo-email-title {
    font-weight: 400;
    font-size: inherit;
    margin-right: 5px;
    margin-bottom: 0;
    line-height: inherit;
    flex: none;
  }
}

.yoo-table.yoo-style4 {
  padding-bottom: 30px;
  margin-bottom: -30px;
  padding-top: 30px;
  margin-top: -30px;


  .table {
    border-bottom: 1px solid $base-color4;
  }

  .table td {
    vertical-align: middle;
    border-color: $base-color4;
  }

  td {
    padding: 8px 7px;

    &:first-child {
      padding-left: 30px;
      width: 56px;
    }

    &:nth-child(2) {
      width: 36px;
    }

    &:nth-child(3) {
      width: 37px;
    }

    &:nth-child(4) {
      width: 185px;
    }

    &:nth-child(5) {
      width: 60px;
    }

    &:last-child {
      padding-right: 30px;
      min-width: 110px;
    }
  }

  tr {
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0px 2px 9px 1px rgba($base-color1, 0.1);
      background-color: #fff;
    }
  }
}

.yoo-email-sent-time {
  font-size: 16px;
  text-align: right;
}

.yoo-new-message {
  background-color: $base-color5;

  .yoo-user.yoo-style2 .yoo-user-name,
  .yoo-email-text.yoo-style1 .yoo-email-title,
  .yoo-email-sent-time {
    font-weight: 600;
    color: $base-color1;
  }
}

// Email Heading
.yoo-email-header.yoo-style1 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  height: 80px;
  align-items: center;
  padding: 0 30px;
}

.yoo-icon-group-wrap.yoo-style1 {
  display: flex;
  align-items: center;
}

.yoo-icon-group.yoo-style1 {
  display: flex;
  align-items: center;

  li:not(:last-child) {
    margin-right: 15px;
  }

  &:not(:last-child) {
    margin-right: 23px;
    padding-right: 23px;
    position: relative;

    &:before {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      right: 0;
      background: $base-color4;
      top: 50%;
      margin-top: -10px;
    }
  }

  .yoo-search.yoo-search-md .yoo-search-input {
    padding-top: 3px;
    padding-bottom: 3px;
  }

  .yoo-search.yoo-style1 .yoo-search-submit {
    font-size: 16px;
  }

  .yoo-search.yoo-style1 {
    width: 300px;
  }

  .yoo-search-wrap {
    margin-left: 10px;
    padding-left: 25px;
    position: relative;

    &:before {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      left: 0;
      background: $base-color4;
      top: 50%;
      margin-top: -10px;
    }
  }
}

.yoo-email-header-mars {
  display: flex;
  align-items: center;

  .yoo-task-checkmark {
    margin-right: 5px;
  }
}

.yoo-email-header-marks-btn,
.yoo-icon-group-icon,
.yoo-email-header-more-list-btn,
.yoo-email-header-setting-btn {
  font-size: 24px;
  display: flex;
  color: #b5b5b5;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    color: $base-color2;
  }
}

.yoo-email-header-marks-btn,
.yoo-email-header-more-list-btn {
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;

  &:after {
    display: none;
  }

  &:focus {
    outline: none;
  }
}

.yoo-email-header-right-list {
  display: flex;
  align-items: center;

  li:not(:first-child) {
    margin-left: 20px;
  }
}

.yoo-email-pagination {
  display: flex;
  align-items: center;
  font-size: 13px;

  .yoo-email-page-number {
    margin-right: 10px;
  }

  .yoo-navigation.yoo-style1 {
    color: $base-color3;

    a {
      &:hover {
        color: $base-color2
      }
    }
  }
}

// Details Email
.yoo-email-details-body {
  padding: 30px;
  border-top: 1px solid $base-color4;

  .yoo-email-user-email {
    display: inline-flex;
    align-items: center;
  }
}

.yoo-email-details-meta-right {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .yoo-user-img {
    height: 32px;
    width: 32px;
  }

  .yoo-email-details-time {
    padding-right: 25px;
    margin-right: 23px;
    position: relative;

    &:after {
      content: '';
      width: 1px;
      height: 20px;
      position: absolute;
      right: 0;
      background: $base-color4;
      top: 50%;
      margin-top: -10px;
    }
  }
}

.yoo-email-details-title {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 0;
  display: flex;
  align-items: flex-start;
  margin-bottom: 9px;

  .yoo-email-details-title-badgh {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: $base-color2;
    background-color: $base-color4;
    border-radius: 4px;
    padding: 4px 6px;
    margin-left: 10px;
    margin-top: 1px;
    line-height: 1.3em;
  }
}

.yoo-email-details-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid $base-color4;
  padding-bottom: 20px;
}

.yoo-email-user-email-toggle-btn {
  display: flex;
  color: $base-color3;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: $base-color2;
  }
}

.yoo-email-details-text {
  font-size: 16px;
  line-height: 1.6em;
  max-width: 780px;
  padding-top: 25px;
  padding-bottom: 25px;
  color: $base-color1;
}

// Email Editor Style
#yoo-email-editor {
  height: 125px;
  border-radius: 5px;
  border: 1px solid $base-color4;
  text-shadow: none;
  margin-top: 30px;
  padding: 20px 25px;

  .ql-editor.ql-blank::before {
    left: 25px;
    right: 25px;
    font-style: normal;
    color: $base-color3;
    font-size: 16px;
    line-height: 1.6em;
    top: 20px;
  }

  .ql-editor {
    line-height: 1.6em;
    padding: 0;
    font-size: 16px;
    line-height: 1.6em;
  }

  strong {
    font-weight: 600;
  }
}

.ql-container {
  font-family: inherit;
}

.yoo-email-btn-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.yoo-email-editor-toolbox-wrap {
  display: flex;
  align-items: center;
}

.yoo-email-editor-btn .yoo-btn {
  min-width: 100px;
}

#yoo-email-editor-toolbox {
  border: none;
  padding: 0;
  display: flex;
  align-items: center;

  button {
    font-weight: 500;
    font-size: 21px;
    padding: 0;
    display: flex;
    width: initial;
    color: $base-color3;
    margin-right: 10px;
    transition: all 0.3s ease;

    &.ql-active,
    &:hover {
      color: $base-color2;
    }
  }
}

.yoo-email-editor-file-group {
  display: flex;
  align-items: center;

  button {
    padding: 0;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 21px;
    color: $base-color3;
    margin-right: 6px;
    display: flex;
    transition: all 0.3s ease;

    &:hover {
      color: $base-color2;
    }

    &:focus {
      outline: none;
    }
  }
}

.yoo-email-user-wrap {
  height: 81px;
  width: 100%;
  padding: 0 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid $base-color4;

  .yoo-user.yoo-style2 .yoo-user-img {
    height: 36px;
    width: 36px;
  }
}

// End Email Editor Style

@media screen and (max-width: 1199px) {

  .yoo-email-text.yoo-style1 .yoo-email-body {
    display: none;
  }

  .yoo-email-header.yoo-style1 {
    padding-left: 90px;
  }

  .yoo-email-header.yoo-style1 .yoo-mobile-hide {
    display: none;
  }
}

@media screen and (max-width: 991px) {

  .yoo-table.yoo-style4 td:nth-child(6) {
    display: none;
  }

  .yoo-icon-group.yoo-style1:not(:last-child) {
    margin-right: 13px;
    padding-right: 13px;
  }

  .yoo-email-details-meta-right .yoo-email-details-time {
    padding-right: 15px;
    margin-right: 13px;
  }
}

@media screen and (max-width: 575px) {
  .yoo-icon-group.yoo-style1 .yoo-search.yoo-style1 {
    width: 100%;
  }

  .yoo-icon-group.yoo-style1 .yoo-search-wrap {
    margin-left: 0;
    padding-left: 0;

  }

  .yoo-icon-group.yoo-style1 li:not(:last-child) {
    margin-right: 10px;

  }

  .yoo-icon-group.yoo-style1 li {
    margin-right: 10px;
  }

  .yoo-icon-group.yoo-style1 li:last-child {
    margin-right: 0px;
  }

  .yoo-email-header-right-list li:not(:first-child) {
    margin-left: 15px;
  }

  .yoo-table.yoo-style4 {
    td {
      padding: 8px 5px;
    }

    td:first-child {
      padding-left: 20px;
      width: 46px;
    }

    td:last-child {
      padding-right: 20px;
      min-width: 95px;
    }

    td:nth-child(5),
    .yoo-user.yoo-style2 .yoo-user-img {
      display: none;
    }
  }

  .yoo-email-header.yoo-style1 .yoo-icon-group.yoo-style1:not(:last-child) {
    margin-right: 0;
    padding-right: 0;
  }

  .yoo-email-header.yoo-style1 .yoo-icon-group.yoo-style1:not(:last-child):before {
    display: none;
  }

  .yoo-email-details-title {
    flex-direction: column-reverse;
  }

  .yoo-email-details-title .yoo-email-details-title-badgh {
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 6px;
  }

  .yoo-email-details-meta-right .yoo-email-details-time {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .yoo-email-header.yoo-style1 {
    padding-left: 80px;
    padding-right: 20px;
  }
}

/*========== End Email  ==========*/
