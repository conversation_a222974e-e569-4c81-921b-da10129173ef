<!DOCTYPE html>
<html class="no-js" lang="en">

<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
<script src="/assets/editor/editor.js"></script>
<link href="/assets/editor/editor.css" type="text/css" rel="stylesheet" />
{% include 'include/header.html' %}
    
<body>
  <!-- <div class="yoo-height-b60 yoo-height-lg-b60"></div>
  <div class="yoo-headersidebar-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div> -->
  <!-- .yoo-headersidebar-toggle -->

  {% include 'include/headersidebar.html' %}
  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title"></h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Gộp mail</h2>
              </div>
            </div>
            <br>
            <form class="form-horizontal">
              <fieldset>

                <!-- Form Name -->
                <!-- <legend>Form Name</legend> -->

                <!-- Select Basic -->
                <div class="form-group">
                  <label class="col-md-4 control-label" for="selectChonlo">Chọn lô</label>
                  <div class="col-md-4">
                    <select id="selectChonlo" name="selectChonlo" class="form-control">
                      <option value="1">Option one</option>
                      <option value="2">Option two</option>
                    </select>
                  </div>
                </div>

                <!-- Select Basic -->
                <div class="form-group">
                  <label class="col-md-4 control-label" for="selectGopsang">Gộp sang</label>
                  <div class="col-md-4">
                    <select id="selectGopsang" name="selectGopsang" class="form-control">
                      <option value="1">Option one</option>
                      <option value="2">Option two</option>
                    </select>
                  </div>
                </div>

                <!-- Button -->
                <div class="form-group">
                  <label class="col-md-4 control-label" for="btnGopMail"></label>
                  <div class="col-md-4">
                    <button id="btnGopMail" name="btnGopMail" style="background-color: #04AA6D;" class="btn btn-primary">Gộp mail</button>
                  </div>
                </div>

              </fieldset>
            </form>






          </div>
        </div>

      </div>
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->
      {% include 'include/footer.html' %}

    </div>
    <!-- .yoo-content -->
    <script>

      $("#btnGopMail").click(function (e) {
        e.preventDefault();
        logoc = getSelectedCombo("selectChonlo")
        logop = getSelectedCombo("selectGopsang")
        url = "/gopMail?logoc=" + logoc + "&logop=" + logop
        html = httpGetJson(url, "Bearer " + window.localStorage.token)
        mess = html.mess
        if (mess == "success") {
          toastr.success("Gộp Mail thành công");
          getGroups()
        } else {
          toastr.success("Gộp Mail thất bại " + mess);
        }
      });


      getGroups()


      function getGroups() {
        json = httpGetJson("/getGroups", "Bearer " + window.localStorage.token)
        var groups = json.groups
        var stringToRender = "";
        var x = 0;
        for (let i = 0; i < groups.length; i++) {
          x = x + 1;
          var onePost = groups[i];
          stringToRender += `<option>${onePost}</option>`;
        }
        $("#selectChonlo").html(stringToRender);
        $("#selectGopsang").html(stringToRender);
        return
        // $.ajax({
        //   url: "/getGroups",
        //   type: "get",
        //   beforeSend: function (request) {
        //     request.setRequestHeader("Authorization", "Bearer " + window.localStorage.token);
        //   },
        //   dataType: "text",
        //   success: function (result) {
        //     var resultJson = JSON.parse(result);
        //     var groups = resultJson.groups
        //     var stringToRender =
        //       "<option>Tất cả</option>";
        //     var x = 0;
        //     for (let i = 0; i < groups.length; i++) {
        //       x = x + 1;
        //       var onePost = groups[i];
        //       stringToRender += `<option>${onePost}</option>`;
        //     }
        //     $("#groups").html(stringToRender);
        //     getDateByGroups();
        //   },
        //   error: function (results) {
        //     window.location.href = "login.html"
        //     return
        //   }
        // });
      }
    </script>

    <!-- Required Scripts -->
</body>

</html>