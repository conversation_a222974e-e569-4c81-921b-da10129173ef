.input-group label {
  font-size: 14px;
  color: $base-color3;
  margin-bottom: 4px;
}

.navbar .form-control {
  min-height: 20px;
}

.form-control {
  border-color: #f0f0f0;
  max-height: 100px;

  &:focus {
    box-shadow: none;
    border-color: $blue-color;
    box-shadow: 0 0 0 4px rgba($blue-color, 0.1);
    color: $base-color2;
  }
}

.form-group {
  margin-bottom: 20px;
}

.form-control {
  padding: 15px;
  min-height: 48px;
  color: $base-color2;
  font-size: 16px;
  background-color: #f0f0f0;
  border-radius: 7px;
}

.level-up .form-control {
  padding: 23px 15px 10px;
}

.level-up textarea.form-control {
  padding: 20px 15px 13px;
}

.level-up .text-muted {
  color: $base-color2 !important;
  font-size: 12px;
  margin-top: 3px;
  margin-bottom: -5px;
  line-height: 1.6em;
  padding: 0 20px;
}

.level-up {
  position: relative;
}

.level-up label {
  position: absolute;
  margin: 0;
  font-size: 16px;
  left: 15px;
  top: 13px;
  color: $base-color2;
  padding: 0;
  transition: all 0.3s ease;
}

.level-up.active1 label,
.level-up.active2 label {
  font-size: 11px;
  top: 1px;
}

.level-up.active2 label {
  color: $base-color3;
}

.form-group-file {
  position: relative;

  &:before {
    content: '';
    position: absolute;
    height: calc(100% - 2px);
    width: 148px;
    background-color: #fff;
    top: 1px;
    left: 5px;
  }

  label {
    position: absolute;
    font-size: 14px;
    background: #fff;
    top: 16px;
    padding: 5px 18px;
    left: 19px;
    border: 1px solid $blue-color;
    border-radius: 4px;
    background-color: $blue-color;
    text-transform: uppercase;
    color: #fff;
    font-weight: 600;
    margin-bottom: 0;
  }

  input {
    border: 1px solid $base-color4;
    padding: 16px 18px;
    min-height: 66px;
    border-radius: 4px;
    padding-left: 55px;

    &:focus {
      outline: none;
    }

  }
}

.form-group-md {
  label {
    font-size: 14px;
    left: 15px;
    top: 9px;
  }

  .form-control {
    padding: 12px 15px;
    min-height: 42px;
    font-size: 14px;
  }

  &.level-up {
    .form-control {
      padding: 17px 15px 8px
    }
  }
}

.form-group-md.level-up.active1 label,
.form-group-md.level-up.active2 label {
  font-size: 11px;
  top: 0px;
}

.form-group-sm {
  label {
    font-size: 13px;
    left: 15px;
    top: 6px;
  }

  .form-control {
    padding: 5px 15px;
    min-height: 36px;
    font-size: 14px;
  }

  &.level-up.active1 label,
  &.level-up.active2 label {
    font-size: 10px;
    top: -2px;
  }
}

.form-group-sm.level-up .form-control {
  padding: 14px 15px 6px;
  font-size: 13px;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #f0f0f0;
  color: $base-color3;
}

.form-control[readonly]::-webkit-input-placeholder {
  color: $base-color3;
}

.form-control[readonly]::-moz-placeholder {
  color: $base-color3;
}

.form-control[readonly]:-ms-input-placeholder {
  color: $base-color3;
}

.form-control[readonly]:-moz-placeholder {
  color: $base-color3;
}

.form-row>.col,
.form-row>[class*=col-] {
  padding-right: 10px;
  padding-left: 10px;
}

.col-form-label {
  font-size: 14px;
  font-weight: 500;
  color: $base-color1;
  padding: 15px 15px;
}

.input-group-text {
  background-color: #e2e2e2;
  border-color: #d8d8d8;
}

.input-group-prepend .input-group-text {
  border-right-width: 2px;
}

.input-group-append .input-group-text {
  border-left-width: 2px;
}

.form-group-sm .input-group-text {
  font-size: 13px;
}

.input-group-append .btn,
.input-group-prepend .btn {
  border-width: 1px;
}

.input-group-prepend [class*=btn-outline-],
.input-group-append [class*=btn-outline-] {
  border-color: $base-color4;
  color: $base-color2;

  &:hover {
    background-color: $base-color5;
    color: $base-color1;
  }
}

.custom-radio .custom-control-input,
.custom-checkbox .custom-control-input {
  position: absolute;
  z-index: 2;
  opacity: 0;
  left: 0;
  top: 0;
  height: 22px;
  width: 22px;
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 30px;
}

.custom-control-label {
  font-size: 14px;
  color: $base-color1;
}

.custom-control-label::before {
  top: 2px;
  left: -30px;
  height: 20px;
  width: 20px;
  border-color: #DBDBDB;
  border-width: 1px;
  z-index: 1;
}

.custom-control-label::after {
  top: 4px;
  left: -24px;
  height: 12px;
  width: 6px;
  border-radius: 0;
  border: 2px solid #fff;
  transform: rotate(45deg);
  border-left: none;
  border-top: none;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 6px;
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::after {
  background-image: none;
  opacity: 1;
}

.custom-radio .custom-control-input:checked~.custom-control-label::after {
  background-image: none;
  opacity: 1;
}

.custom-radio .custom-control-input:checked~.custom-control-label::before {
  background-color: transparent;
}

.custom-radio .custom-control-label::after {
  border: none;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: $blue-color;
  left: -25px;
  top: 7px;
}

.custom-radio .custom-control-label::before {
  height: 20px;
  width: 20px;
}

.custom-control-input:checked~.custom-control-label::before {
  border-color: $blue-color;
  background-color: $blue-color;
}

.custom-control-input:disabled~.custom-control-label {
  color: $base-color1;
}

.custom-control.form-check-inline {
  display: inline-block;
  margin-right: 30px;
}

.custom-control.no-labels {
  margin-right: 0;
  padding-left: 18px;
}

.custom-control.no-labels .custom-control-label::before {
  left: -18px;
}

.custom-control.no-labels .custom-control-label::after {
  left: -11px;
}

.input-group-text {
  padding: 5px 15px;
}

.custom-control.no-labels.custom-radio {
  padding-left: 20px;
}

.custom-control.no-labels.custom-radio .custom-control-label::before {
  left: -20px;
}

.custom-control.no-labels.custom-radio .custom-control-label::after {
  left: -15px;
}

.custom-control.no-labels.yoo-style1 .hydrated {
  position: absolute;
  left: -20px;
  z-index: 1;
  font-size: 24px;
  top: 0px;
  color: $base-color3;
}

.custom-control.no-labels.yoo-style1 .custom-control-label::before {
  border-color: transparent;
  height: 20px;
  width: 20px;
}

.custom-control.no-labels.yoo-style1 .custom-control-input:checked~.custom-control-label::before {
  border-color: #007aff;
  background-color: #007aff;
}

.custom-control.no-labels.yoo-style1 .custom-control-input:checked~.custom-control-label .hydrated {
  color: #007aff;
}

.custom-control.no-labels.yoo-style1 .custom-control-label::after {
  left: -11px;
  top: 4px;
}

.custom-control.no-labels.yoo-style1 {}

.form-row {
  margin-right: -10px;
  margin-left: -10px;
}

.yoo-select {
  position: relative;

  .form-control {
    padding-left: 15px;
  }
}

.yoo-select:before {
  content: '';
  position: absolute;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid $base-color3;
  right: 20px;
  top: 23px;
  z-index: 1;
  pointer-events: none;
}

.yoo-select:after {
  content: '';
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: #f0f0f0;
  top: 17px;
  right: 3px;
  pointer-events: none;
}

.navbar .form-control {
  background-color: #fff;
  height: 42px;
}

.form-group-sm {
  .yoo-select:after {
    top: 13px;
  }

  .yoo-select:before {
    top: 15px;
  }
}
