<!DOCTYPE html>
<html class="no-js" lang="en">

{% include 'include/header.html' %}

<body>
  <style>
    .form-control {
      width: unset;
    }
  </style>
  <div class="yoo-height-b60 yoo-height-lg-b60"></div>
  <div class="yoo-headersidebar-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div>
  <!-- .yoo-headersidebar-toggle -->

  {% include 'include/headersidebar.html' %}
  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title"></h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Danh sách mail (<div id="total" style="color: red;"></div>)
                </h2>
              </div>
              <div class="yoo-card-heading-right btn-toolbar pull-right">
                <div class="row">
                  <input type="text" id="txtmailsearch" class="form-control" placeholder="Nhập mail cần tìm">
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-sm" id="copy"
                    onclick="Search()">Tìm kiếm</button>
                </div>
              </div>
            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th scope="col">#</th>
                      <th scope="col">Email</th>
                      <th scope="col">Pass</th>
                      <th scope="col">EmailKP</th>
                      <th scope="col">#</th>
                    </tr>
                  </thead>
                  <tbody id="tbody"></tbody>
                </table>

                <div id="page"></div>

              </div>
            </div>
          </div>
        </div>

      </div>
      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>

      <!-- .yoo-card -->

      {% include 'include/footer.html' %}
    </div>
    <!-- .yoo-content -->
    <script>

      $("#txtmailsearch").keyup(function (event) {
        if (event.keyCode == 13) {
          Search();
        }
      });

      var search = window.location.search;
      var pages = Regex(search, /page=(\d+)/)
      if (pages != null && pages.length > 1) {
        GetFile(pages[1])
      } else {
        GetFile("1")
      }
      function GetFile(page) {
        $.ajax({
          url: "/mail/manager/" + page,
          type: "get",
          dataType: "text",
          success: function (results) {
            const obj = JSON.parse(results);
            var count = obj.count
            var result = obj.result;
            var total = obj.total;
            var skip = obj.skip;
            var stringToRender = ""
            var paginations = `<ul class="pagination pagination-lg">`

            var pageshow = pagination(page, count)


            for (let i = 0; i < pageshow.length; i++) {
              var pageht = pageshow[i]
              if (pageht == page || pageht == "...") {
                paginations = paginations + `<li class="page-item disabled"><a class="page-link" href="#" tabindex="-1">${pageht}</a></li>`
              } else {
                paginations = paginations + `<li class="page-item"><a class="page-link" href="/mailmanager.html?page=${pageht}">${pageht}</a></li>`
              }
            }
            paginations = paginations + `</ul>`

            for (let i = 0; i < result.length; i++) {
              const onePost = result[i];
              //  console.log(onePost)
              stringToRender += `<tr id="${onePost.id}"><td>${skip + i + 1}</td><td>${onePost.email}</td><td>${onePost.pass}</td><td>${onePost.emailkp}</td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a></td>`;
            }

            $("#total").html(total);
            $("#tbody").html(stringToRender);
            $("#page").html(paginations);
          }
        });
      }
      function Search() {
        var mailsearch = $('#txtmailsearch').val();
        if (mailsearch == "") {
          if (pages != null && pages.length > 1) {
            GetFile(pages[1])
          } else {
            GetFile("1")
          }
          return
        }
        $("#tbody").html("");
        $("#page").html("<b>Đang tìm kiếm...</b></br></br>");
        $.ajax({
          url: "/mail/search",
          type: "post",
          dataType: "text",
          data: {
            mailsearch: mailsearch,
          },
          success: function (results) {
            const obj = JSON.parse(results);
            var result = obj.result;
            var stringToRender = ""
            if (result == null) {
              $("#page").html("<b>Không tìm thấy kết quả nào</b></br></br>");
              return
            }

            for (let i = 0; i < result.length; i++) {
              const onePost = result[i];
              //  console.log(onePost)
              stringToRender += `<tr id="${onePost.id}"><td>${i + 1}</td><td>${onePost.email}</td><td>${onePost.pass}</td><td>${onePost.emailkp}</td><td><a nohref style='cursor:pointer;color:blue;text-decoration:underline' onClick='Del("${onePost.id}");'>Xóa</a></td>`;
            }
            $("#tbody").html(stringToRender);
            $("#page").html("");
          }
        });
      }
      function Del(id) {
        var r = confirm("Xác nhận xóa!");
        if (r !== true) {
          return
        }
        $.ajax({
          url: "/mail/delete/" + id,
          type: "get",
          dataType: "text",
          success: function (results) {
            const obj = JSON.parse(results);
            var result = obj.msg;
            if (result == "Success") {
              toastr.success("Xóa thành công");
              location.reload();
            } else {
              toastr.success(result);
            }
          }
        });
      }
    </script>
    <!-- Required Scripts -->
</body>

</html>