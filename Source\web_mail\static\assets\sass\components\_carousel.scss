.carousel-control-prev,
.carousel-control-next {
  font-size: 24px;
  opacity: 1;
  background-color: #fff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  color: $base-color2;
  top: 50%;
  transform: translateY(-50%);

  &:hover {
    color: $base-color1;
  }

  &:focus {
    color: $base-color2;
  }

  i {
    transition: all 0.3s ease;
  }
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}

.carousel-caption {
  bottom: 24px;

  h5 {
    color: #fff;
    margin-bottom: 5px;
  }

  p {
    color: rgba(255, 255, 255, 0.7);
  }
}

.carousel-indicators {
  margin-bottom: 20px;
}

.carousel-indicators li {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  margin: 0 5px;
}

.carousel-control-prev,
.carousel-control-next {
  font-size: 22px;
  background-color: rgba($base-color, 0.6);
  width: 44px;
  height: 44px;
  color: #fff;
  border: 1px solid #fff;
  transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-next:focus {
  background-color: rgba($base-color, 0.9);
  color: #fff;
}
