//change device
ChangeInfo()
//chờ reboot
WaitReboot()
//chờ internet
WaitInternet()
//mở chrome
OpenApp("com.android.chrome")
//chờ 2s
Wait(2000)
//tạo vòng lặp for
//đặt tên cho vòng lặp for
//lặp lại 100 lần
for=ttchrome,end=100
{
    //tìm click Accept & continue
    SearchAndClick("Accept & continue")
    //tìm click No thanks
    // nếu tồn tại 2 text dưới thì click vào no thanks
    //ở đây ta thêm text add account và no thanks, nhưng khi chạy nó lại ra khác
    //thêm 1 lệnh tìm kiếm nữa
    SearchAndClick("Sign in", "No Thanks")
    SearchAndClick("Add account", "No Thanks")
    SearchAndClick("com.android.chrome:id/search_box")
    //tốt nhất nên  lấy những text mà ít xuất hiện chổ khác để ko bị click nhầm
    //nếu bàn phím mở và tìm thấy text com.android.chrome:id/url_bar
    //thì vào if để send text
    //đặt tìm text ở if nên dùng lệnh tìm và tiếp tục
    //lý do chỉ tìm rồi vào if chứ ko click
    if=sendtext|CheckKeyBoard(1) && SearchAndContinue("com.android.chrome:id/url_bar")
    {
        //send text random
        SendText("tin tuc", "phim anh", "truyen tranh")
        //bấm phím enter key 66 là enter
        SendKey(66)
        //chờ 3-5s để load
        Wait(3000-5000)
    }
    //nếu tìm thấy text thì vào if xử lý 
    if=tuongtac|SearchAndContinue("Tất cả", "Đăng nhập")
    {
        //ở đây ta đặt một vòng for để vuốt lên xuống tương tác
        //cho lặp lại 5 lần
        for=swipe,end=5
        {
            //cuộn xuống
            Swipedown(500 500 5 10)
            //chờ 2s
            Wait(2000)
            //cuộn xuống
            Swipedown(500 500 5 10)
            //chờ 2s
            Wait(2000)
            //cuộn lên
            Swipeup(500 500 5 10)
            //chờ 2s
            Wait(2000)
            Swipeup(500 500 5 10)
            //chờ 2s
            Wait(2000)
        }
        //khi lặp lại 5 lần cuộn lên xuống
        //đóng chrome
        CloseApp("com.android.chrome")
        //đặt return để hoàn thành script
        Return()
    }
}
//ok bây giờ test thử
//như vậy chúng ta đã hoàn thành bước tạo script đơn giản
//để lặp đi lặp lại nhiều lần 1 script thì các bạn có thể chọn số lần lặp
//hoặc lặp vô hạn ở đây