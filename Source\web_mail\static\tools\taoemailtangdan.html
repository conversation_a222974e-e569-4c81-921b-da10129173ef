<!DOCTYPE html>
<html class="no-js" lang="en">
{{template "header" .}}

<body>
  <div class="yoo-height-b60 yoo-height-lg-b60"></div>
  <div class="yoo-sidebarheader-toggle">
    <div class="yoo-button-bar1"></div>
    <div class="yoo-button-bar2"></div>
    <div class="yoo-button-bar3"></div>
  </div>
  <!-- .yoo-sidebarheader-toggle -->
  {{template "sidebarheader" .}}
  <div class="yoo-content yoo-style1">
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="yoo-uikits-heading">
        <h2 class="yoo-uikits-title">{{.Title}}</h2>
      </div>
    </div>
    <div class="yoo-height-b30 yoo-height-lg-b30"></div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Mail input</h2>
              </div>
            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <form>
                  <div class="form-row">
                    <div class="col">
                      <input type="text" id="txtdauemail" class="form-control" placeholder="Nhập đầu email">
                    </div>
                    <div class="col">
                      <input type="text" id="txtduoiemail" class="form-control" placeholder="Nhập đuôi email"
                        value="@hotmail.com">
                    </div>
                  </div>
                  <br>
                  <div class="form-row">
                    <div class="col">
                      <input type="text" id="txtsobatdau" class="form-control" placeholder="Nhập số bắt đầu" value="1">
                    </div>
                    <div class="col">
                      <input type="text" id="txtsoketthuc" class="form-control" placeholder="Nhập số kết thúc"
                        value="1000">
                    </div>
                  </div>
                  <br>
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-lg" id="check"
                    onclick="taoemailtangdan(event)">Tạo ngay</button>
                  <br>
                  <br>
                </form>
                <div class="yoo-height-b5 yoo-height-lg-b5"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>
      <div class="row">
        <div class="col-lg-12">
          <div class="yoo-card yoo-style1">
            <div class="yoo-card-heading">
              <div class="yoo-card-heading-left">
                <h2 class="yoo-card-title">Mail output</h2>
              </div>
              <div class="yoo-card-heading-right btn-toolbar pull-right">
                <div class="row">
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-secondary btn-sm"
                    style="margin: 0px 2px 0px 0px;" id="del" onclick="$('#output').val('');">Del</button>
                  <button type="button" ripple="ripple" ripple-color="#fff" class="btn btn-primary btn-sm" id="copy"
                    onclick="CopyTextareaToClipboard('output')">Copy</button>
                </div>
              </div>
            </div>
            <div class="yoo-card-body">
              <div class="yoo-padd-lr-20">
                <div class="yoo-height-b20 yoo-height-lg-b20"></div>
                <form>
                  <div class="form-group level-up">
                    <label for="exampleFormControlTextarea1"></label>
                    <textarea class="form-control" style="max-height: none;" id="output" rows="15"></textarea>
                  </div>
                </form>
                <div class="yoo-height-b5 yoo-height-lg-b5"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- .yoo-card -->
      <div class="yoo-height-b30 yoo-height-lg-b30"></div>
      <!-- .yoo-card -->
      {{template "footer" .}}
    </div>
    <!-- .yoo-content -->
    <script language="javascript">
      function taoemailtangdan() {
        var dauemail = $('#txtdauemail').val();
        var duoiemail = $('#txtduoiemail').val();
        var sobatdau = $('#txtsobatdau').val();
        var soketthuc = $('#txtsoketthuc').val();
        var result = "";
        for (i = sobatdau; i <= soketthuc; i++) {
          result = result + dauemail + i + duoiemail + "\n";
        }
        $('#output').html(result.trim());
      }

    </script>
    <!-- Required Scripts -->
</body>

</html>
